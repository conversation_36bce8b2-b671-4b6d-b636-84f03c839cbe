---
type: "agent_requested"
description: "Example description"
---
# UNIQ POS Android Application Architecture

## Project Overview
This is an Android Point of Sale (POS) application built for UNIQ, a comprehensive retail management system. The application follows modern Android development practices with MVVM architecture, dependency injection, and clean architecture principles.

## Technology Stack
- **Language**: Ko<PERSON><PERSON> (primary), Java (legacy components)
- **Architecture**: MVVM (Model-View-ViewModel) with Repository pattern
- **Dependency Injection**: Dagger 2
- **Database**: Room (SQLite)
- **Networking**: Retrofit 2 + OkHttp, Ktor WebSocket client
- **UI**: Android Views with Data Binding and View Binding
- **Image Loading**: Glide
- **Async Operations**: Kotlin Coroutines
- **Build System**: Gradle with Kotlin DSL
- **Min SDK**: 24, Target SDK: 35, Compile SDK: 34

## Project Structure

### Root Level
```
android-pos/
├── app/                    # Main application module
├── materialanimation/      # Custom animation library module
├── fastlane/              # CI/CD automation
├── gradle/                # Gradle wrapper
├── tools/                 # Build scripts and tools
└── build.gradle           # Root build configuration
```

### Main Application Module (`app/`)
```
app/
├── src/
│   ├── main/              # Production source
│   ├── debug/             # Debug build variant
│   ├── development/       # Development build variant
│   ├── production/        # Production build variant
│   ├── alive/             # Beta/staging build variant
│   ├── androidTest/       # Instrumented tests
│   └── test/              # Unit tests
├── build.gradle           # App module build configuration
├── proguard-rules.pro     # ProGuard configuration
└── google-services.json   # Firebase configuration
```

### Source Code Architecture (`app/src/main/java/com/uniq/uniqpos/`)
```
com.uniq.uniqpos/
├── app/                   # Application class and global configuration
├── data/                  # Data layer (Repository pattern)
│   ├── local/            # Local data sources (Room database, SharedPreferences)
│   └── remote/           # Remote data sources (API services, models)
├── di/                   # Dependency injection modules (Dagger 2)
├── model/                # Domain models and data classes
├── receiver/             # Broadcast receivers
├── service/              # Background services
├── sync/                 # Data synchronization logic
├── util/                 # Utility classes and extensions
├── view/                 # UI layer (Activities, Fragments, Adapters)
│   ├── global/           # Base classes and shared UI components
│   ├── main/             # Main dashboard
│   ├── login/            # Authentication
│   ├── transaction/      # Transaction management
│   ├── payment/          # Payment processing
│   ├── member/           # Customer management
│   ├── productcatalogue/ # Product management
│   ├── setting/          # App settings
│   └── [other features]/ # Feature-specific UI modules
└── viewmodel/            # ViewModels for MVVM architecture
```

## Architecture Patterns

### MVVM (Model-View-ViewModel)
- **Model**: Data classes, entities, and business logic
- **View**: Activities, Fragments, and XML layouts
- **ViewModel**: Manages UI-related data and business logic

### Repository Pattern
- Abstracts data sources (local database, remote API)
- Provides single source of truth for data
- Handles data synchronization between local and remote sources

### Dependency Injection (Dagger 2)
- `AppComponent`: Main application component
- `AppModule`: Application-level dependencies
- `ActivityBuilderModule`: Activity-specific dependencies
- `FragmentBuilderModule`: Fragment-specific dependencies
- `ViewModelModule`: ViewModel dependencies
- `ServiceModule`: Service dependencies
- `DaoModule`: Database access object dependencies

## Build Variants and Flavors

### Product Flavors
1. **production**: Production environment
   - Base URL: `https://api.uniq.id/`
   - Web URL: `https://client.uniq.id/`
   - Package: `id.uniq.uniqpos`

2. **development**: Development environment
   - Base URL: `https://api.uniqdev.web.id/`
   - Web URL: `https://uniqdev.web.id/`
   - Package: `id.uniq.uniqpos.development`

3. **alive**: Beta/staging environment
   - Base URL: `https://api-staging.uniq.id/`
   - Web URL: `https://staging.uniq.id/`
   - Package: `id.uniq.uniqpos.beta`

### Build Types
- **debug**: Development builds with debugging enabled
- **release**: Production builds with ProGuard/R8 optimization

## Key Features and Modules

### Core Business Features
- **Transaction Management**: POS transaction processing
- **Payment Processing**: Multiple payment methods support
- **Product Catalog**: Product management and inventory
- **Member Management**: Customer relationship management
- **Order Sales**: Online order management
- **Billing**: Invoice and billing management
- **Cash Drawer**: Cash management operations
- **Reservation**: Table and booking management
- **Piutang**: Accounts receivable management

### Technical Features
- **Printer Integration**: Thermal printer support via Bluetooth
- **Barcode Scanning**: Product scanning capabilities
- **Kitchen Display**: Kitchen order management
- **Self Order**: Customer self-service ordering
- **WebSocket**: Real-time communication
- **Firebase Integration**: Analytics, Crashlytics, Remote Config
- **Offline Support**: Local database with sync capabilities

## Database Schema (Room)
- Local SQLite database using Room persistence library
- Entity classes in `data/local/entity/`
- DAO interfaces in `data/local/dao/`
- Database migrations in `data/local/migration/`
- Type converters in `data/local/converter/`

## Networking
- **Retrofit 2**: REST API communication
- **OkHttp**: HTTP client with logging interceptor
- **Ktor**: WebSocket client for real-time features
- **Gson**: JSON serialization/deserialization

## UI/UX Guidelines
- Material Design components
- Data Binding for efficient view updates
- View Binding for type-safe view references
- Custom adapters extending global base classes
- Consistent theming across build variants

## Testing Strategy
- Unit tests in `src/test/`
- Instrumented tests in `src/androidTest/`
- JUnit 4 and JUnit Jupiter for testing framework

## Build and Deployment
- Gradle build system with version management
- Git-based versioning using tags
- Fastlane for automated deployment
- Multiple signing configurations for different environments
- ProGuard/R8 for code obfuscation and optimization

## Development Guidelines
1. Follow MVVM architecture pattern
2. Use dependency injection for loose coupling
3. Implement Repository pattern for data access
4. Use Kotlin coroutines for asynchronous operations
5. Follow Android Architecture Components best practices
6. Maintain separation of concerns between layers
7. Use data binding for efficient UI updates
8. Implement proper error handling and logging
9. Follow Material Design guidelines
10. Write unit tests for business logic

## AI Development Rules

### Code Style and Standards
- Always use Kotlin for new code unless working with existing Java files
- Follow Android Kotlin style guide and naming conventions
- Use meaningful variable and function names
- Add proper documentation for public APIs
- Use data classes for model objects
- Prefer immutable data structures where possible

### Architecture Compliance
- New features must follow MVVM pattern
- Use Repository pattern for data access
- Inject dependencies through Dagger 2
- ViewModels should not hold references to Views/Contexts
- Use LiveData or StateFlow for reactive programming
- Separate business logic from UI logic

### Database Operations
- All database operations must use Room DAOs
- Use suspend functions for database operations
- Implement proper database migrations when schema changes
- Use transactions for multiple related database operations
- Cache frequently accessed data appropriately

### Network Operations
- Use Retrofit for REST API calls
- Implement proper error handling for network requests
- Use coroutines for asynchronous network operations
- Implement offline-first approach where applicable
- Handle network state changes gracefully

### UI Development
- Use View Binding or Data Binding for view references
- Follow Material Design guidelines
- Implement proper loading states and error handling
- Use RecyclerView with DiffUtil for lists
- Implement proper navigation using Navigation Component
- Handle configuration changes properly

### Testing Requirements
- Write unit tests for ViewModels and Repository classes
- Mock external dependencies in tests
- Test error scenarios and edge cases
- Use appropriate testing frameworks (JUnit, Mockito, etc.)
- Maintain test coverage for critical business logic

### Performance Considerations
- Optimize RecyclerView performance with proper ViewHolder patterns
- Use appropriate image loading strategies with Glide
- Implement proper memory management
- Avoid memory leaks in long-running operations
- Use ProGuard/R8 for release builds

### Security Guidelines
- Never hardcode sensitive information
- Use proper encryption for sensitive data storage
- Validate all user inputs
- Implement proper authentication and authorization
- Follow Android security best practices
