package com.uniq.uniqpos.view.transaction

import android.Manifest
import android.content.res.AssetManager
import android.graphics.Color
import android.os.Build
import android.util.SparseArray
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.bugsnag.android.Bugsnag
import com.google.android.material.snackbar.Snackbar
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import com.uniq.uniqpos.BuildConfig
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.*
import com.uniq.uniqpos.data.local.sharedpref.SharedPref
import com.uniq.uniqpos.data.remote.model.*
import com.uniq.uniqpos.data.remote.model.PromotionProduct
import com.uniq.uniqpos.data.remote.repository.*
import com.uniq.uniqpos.databinding.ListItemBillBinding
import com.uniq.uniqpos.databinding.ListItemCategoryBinding
import com.uniq.uniqpos.model.*
import com.uniq.uniqpos.util.*
import com.uniq.uniqpos.util.lifecycle.BaseViewModel
import com.uniq.uniqpos.util.lifecycle.Event
import com.uniq.uniqpos.util.lifecycle.SingleLiveAction
import com.uniq.uniqpos.util.lifecycle.SingleLiveEvent
import com.uniq.uniqpos.util.lifecycle.Success
import com.uniq.uniqpos.util.receipt.NotaManager
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.global.GlobalViewHolder
import com.uniq.uniqpos.view.transaction.utils.PromotionUtil
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import retrofit2.await
import timber.log.Timber
import javax.inject.Inject
import kotlin.math.abs
import java.io.File

/**
 * Created by ANNASBlackHat on 12/10/2017.
 */

open class TransactionViewModel @Inject constructor(
    private var productRepository: ProductRepository,
    private var salesRepository: SalesRepository,
    private var settingRepository: SettingRepository,
    private var outletRepository: OutletRepository,
    private val promotionRepository: PromotionRepository,
    private val sharedPref: SharedPref,
    private val assetManager: AssetManager
) : BaseViewModel() {

    var selectedCategoryId: Int = 0
    var selectedCategoryExtraId: Int = -1
    var selectedCategoryPosition: Int = 0
    var selectedCategoryExtraPosition: Int = 0
    var selectedTag: Int = -1

    var isSalesEdit = false

    private val categories = ArrayList<SubCategoryEntity>()
    val categoriesTmp = ArrayList<SubCategoryEntity>()
    val gratuities = ArrayList<GratuityEntity>()
    val taxes = ArrayList<TaxEntity>()
    val taxSaleList = ArrayList<TaxSales>()
    val multiplePriceList = ArrayList<MultiplePriceEntity>()
    val products = ArrayList<ProductEntity>()
    val productsUnique: List<ProductEntity> //unique by product id (variant not included)
        get() = products.distinctBy { it.productId }
    val variants = ArrayList<ProductVariantEntity>()
    val orders = ArrayList<Order>()
    val taxesEnable = SparseArray<Boolean?>()
    var discount = Discount()
    var salesEdit: SalesEntity? = null
    var choosenEmployee: EmployeeEntity? = null
    var isMergeBill = false
    var mergeIds = ArrayList<String>()
    val linkMenuList = ArrayList<LinkMenuEntity>()
    val linkMenuProductList = ArrayList<LinkMenuProduct>()
    val linkMenuSelected = ArrayList<LinkMenuProduct>()
    var contactSelfOrder: String? = null
    private var notesHistory = ArrayList<SalesNotes>()
    var customerName: String? = null
    var diningTable: String? = null
    var discAndVoucherInfoHistory = ArrayList<String>()

    val promoApplied = ArrayList<Promotion>()
    var memberId: String? = null
    var memberDetail: Member? = null
    var promotionList = ArrayList<PromotionEntity>()
    var linkMenuCurrent: LinkMenuEntity? = null
    var autoShowFreeEnable = true
    var promotionUsage = ArrayList<PromotionUsageResponse>()
    val salesTagList = ArrayList<SalesTagEntity>()

    val customerNames = ArrayList<String>()

    val refreshCategory =SingleLiveAction()
    val refreshProduct =SingleLiveAction()
    val refreshOrder =SingleLiveAction()
    val searchProduct = SingleLiveEvent<String>()
    val refreshTransactionCartCount = SingleLiveEvent<Int>()
    val refreshPendingPrintCount = SingleLiveEvent<Int>()
    val refreshFabScan = SingleLiveEvent<Boolean>()
    val printTask = SingleLiveEvent<Pair<List<PendingPrintEntity>, Boolean>>()
    val barcodeScan = SingleLiveEvent<String>()
    val barcodeResult =
        SingleLiveEvent<Pair<ProductEntity, Boolean>>() // boolean : set true if product is as a variant
    val pDialogTask = SingleLiveEvent<String>()


    val taskMemberFound = SingleLiveEvent<ServerResponse<Member>>()
    val taskMemberRemoved = SingleLiveAction()
    val taskPromotionResult = SingleLiveEvent<Exception?>()
    val taskShowMessage = SingleLiveEvent<Exception>()
    val taskReceiveCart =SingleLiveAction()
    val taskCartSaved = SingleLiveEvent<SalesEntity>()
    val taskVoidVoucher = SingleLiveEvent<Promotion>()
    val taskRefreshPromo = SingleLiveAction()
    val taskRefreshBill =SingleLiveAction()
    val taskRefreshTax =SingleLiveAction()
    val dialogMessageTask = SingleLiveEvent<String>()
    val taskShowVoucherDetail =SingleLiveAction()
    val taskFoundSelfOrder = SingleLiveEvent<ArrayList<Order>>()
    val taskShowTutorialProduct = SingleLiveEvent<Boolean>()
    val taskAddBillNoVariant = SingleLiveEvent<Pair<ProductEntity, Int>>()
    val taskSendToLocalServer = SingleLiveEvent<TmpSalesEntity>()
    val taskPromotionListRefresh = SingleLiveAction()
    val taskShowFreeItemDialog = SingleLiveEvent<Int>()
    val taskPromotionUsageDialog = SingleLiveEvent<ArrayList<PromotionUsage>>()
    val taskPhotoUpdated= SingleLiveEvent<String>()

    //view model helper
    val vmKitchen = TransactionViewModelKitchen(settingRepository, sharedPref, this, this, assetManager)

    val outlet: Outlet? by lazy { sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java) }

    private val promoMemberTypes = listOf(
        Constant.PROMO_TYPE_FREE_MEMBER,
        Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER,
        Constant.PROMO_TYPE_DISCOUNT_MEMBER
    )

    private val _generatedImageUrl = MutableLiveData<Event<String>>()
    val generatedImageUrl: LiveData<Event<String>> = _generatedImageUrl

    fun getSalesEntity(
        customer: String = salesEdit?.customer.safe().takeMax(20),
        qtyCustomer: Int = salesEdit?.customersQty.safe(1),
        timePrediction: Int = salesEdit?.timePrediction.safe(),
        table: String = salesEdit?.table.safe(),
        note: String = salesEdit?.note.safe(),
        isToPayment: Boolean = false
    ): SalesEntity {

        val employee = sharedPref.getJson(SharedPref.EMPLOYEE_DATA, Employee::class.java)
        val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)

        var timeCreated = System.currentTimeMillis()
        val noNota = salesEdit?.let {
            timeCreated = it.timeCreated
            it.noNota
        } ?: kotlin.run {
            Utils.generateNoNota().also { Timber.i("generate new nota: $it") }
        }
        Timber.i("No Nota : %s", noNota)
        Timber.i("Is Edit Note List ? " + (salesEdit != null))

        choosenEmployee?.let { chosenEmployee ->
            orders.filter { it.employeeId == 0 }.forEach { order ->
                order.employeeId = chosenEmployee.employeeId
                order.employeeName = chosenEmployee.name
                order.extra.filter { it.employeeId == 0 }.forEach { extra ->
                    extra.employeeId = chosenEmployee.employeeId
                    extra.employeeName = chosenEmployee.name
                }
            }
        }

        val sales = SalesEntity(noNota,
            orders,
            calculateGrandTotal(),
            customer,
            qtyCustomer,
            timePrediction,
            taxSaleList.filter { it.id != null && it.total > 0 } as java.util.ArrayList<TaxSales>,
            table = table,
            timeCreated = timeCreated)

        sales.employeeID = employee?.employeeId.safe()
        sales.employeeName = employee?.name.safe()
        sales.outletID = outlet?.outletId ?: 0
        sales.outletName = outlet?.name.safe()
        sales.discount = discount
        sales.memberId = memberId
        sales.memberDetail = memberDetail
        sales.note = note

        if (selectedTag >= 0) {
            sales.salesTag = salesTagList.getSafe(selectedTag)
        }

        //simplify: use less memory
        if (isToPayment) {
//            val promoAppliedSimplify = ArrayList<Promotion>()
//            promoApplied.forEach { promoAppliedSimplify.add(it.copy(promotionDetail = null)) }
//            sales.promotions = promoAppliedSimplify
            sales.promotions =
                promoApplied.simplify(orders.map { it.product?.productDetailId.safe() })
        } else {
            sales.promotions = promoApplied.copy()
        }

        //avoid duplicate promotion
        sales.promotions?.forEach {
            if (it.promotionId == 0 && it.code.safeToInt() > 0) {
                it.promotionId = it.code.safeToInt()
            }
        }

        if (promotionList.isNotEmpty()) {
            taxSaleList.filter { tax -> tax.identifierType == "promotion" }.forEach { tax ->
                promotionList.firstOrNull {
                    it.promotionId == tax.identifierId.safeToInt() &&
                            sales.promotions?.any { p -> p.promotionId == it.promotionId } == false
                }?.let { promo ->
                    val promotionProduct = ArrayList<PromotionProduct>()
                    promo.promotionProduct.filter { p -> orders.any { o -> o.product?.productDetailId == p.productDetailId } }
                        .forEach { product ->
                            promotionProduct.add(PromotionProduct(productDetailFkid = product.productDetailId.safe()))
                        }

                    if (promo.discountType == null || promo.discountType == "") {
                        promo.discountType = if (promo.usePercent == 1) "percent" else "nominal"
                    }

                    Timber.i("'${promo.name}' promo disc type: ${promo.discountType}")
                    sales.promotions?.add(
                        Promotion(
                            typeId = promo.promotionTypeId.safe(),
                            promotionValue = tax.total,
                            promoNominal = tax.total,
                            promotionId = promo.promotionId,
                            promotionDetail = PromotionDetail(products = promotionProduct, productsFree = listOf()),
                            appliedByManual = false,
                            name = promo.name,
                            discountType = promo.discountType
                        )
                    )
                }
            }
        }

//        val lengthSales = Gson().toJson(sales).length
//        Bugsnag.leaveBreadcrumb("length sales: $lengthSales")
//        Timber.i("length sales: $lengthSales | isPayment $isToPayment | promotions : ${Gson().toJson(sales.promotions)} | item size: ${sales.orderList?.size}")
        return sales
    }

    fun resetState(){
        promotionList.clear()
        promoApplied.clear()
        orders.clear()
        products.clear()
        discount = Discount()
    }

    fun initDefaultTax() {
        gratuities.filter { !(taxesEnable[it.gratuityId] ?: false) }.forEach {
            taxesEnable.put(
                it.gratuityId,
                (it.taxStatus == "permanent" || it.taxStatus == "temp_active")
            )
        }
        Timber.i("tax enabled : ${Gson().toJson(taxesEnable)}")
    }

    //return grand total sum by all product which has disc or voucher active
    private fun getTotalWithDiscAndVoucher(): Pair<Int, Int> {
        var grandTotalWithDisc = 0
        var grandTotalWithVcr = 0
        var log = ""
        orders.forEachIndexed { _, order ->
            val subtotal =
                if (order.isItemVoid && order.subTotal >= 0) order.subTotal * -1 else order.subTotal
            var tmpGrandTotal = subtotal
            if (order.product?.discount == "on") grandTotalWithDisc += subtotal
            if (order.product?.voucher == "on") grandTotalWithVcr += subtotal
            log += "\n\nsubtotal : $subtotal | disc : ${order.product?.discount} | vouch : ${order.product?.voucher} | grandTotalWithDisc : $grandTotalWithDisc | grandTotalWithVcr : $grandTotalWithVcr"
            order.extra.forEachIndexed { _, extra ->
                val subtotalExtra =
                    if (extra.isItemVoid && extra.subTotal >= 0) extra.subTotal * -1 else extra.subTotal
                tmpGrandTotal += subtotalExtra
                if (extra.product?.discount == "on") grandTotalWithDisc += subtotalExtra
                if (extra.product?.voucher == "on") grandTotalWithVcr += subtotalExtra
                log += "\nsubtotal extra : $subtotalExtra"
            }
            log += "\n(${order.product?.name}) grandTotalWithDisc : $grandTotalWithDisc | grandTotalWithVcr : $grandTotalWithVcr"
            var disc = calculateDiscount(order)
            log += "\ncalculateDiscount : $disc"

            //minus by disc item
            val helper = if (order.isItemVoid && disc >= 0) -1 else 1
            if (order.product?.discount == "on") grandTotalWithDisc -= disc * helper
            if (order.product?.voucher == "on") grandTotalWithVcr -= disc * helper

            log += "\n(${order.product?.name}) grandTotalWithDisc : $grandTotalWithDisc | grandTotalWithVcr : $grandTotalWithVcr | void : ${order.isItemVoid}"
        }
        Timber.d("[DISC] $log")
        return Pair(grandTotalWithDisc, grandTotalWithVcr)
    }

    private fun getTotalWithPromotion(productList: List<com.uniq.uniqpos.data.local.entity.PromotionProduct>): Int {
        if (productList.isEmpty()) return 0

        var total =
            orders.filter { order -> productList.any { p -> p.productDetailId == order.product?.productDetailId } }
                .sumOf { it.subTotal }
        total += orders.flatMap { it.extra }
            .filter { order -> productList.any { p -> p.productDetailId == order.product?.productDetailId } }
            .sumOf { it.subTotal }
        return total
    }

    private fun getTotalWithPromotions(productList: List<PromotionProduct>): Int {
        var total =
            orders.filter { order -> productList.any { p -> p.productDetailFkid == order.product?.productDetailId } }
                .sumOf { it.subTotal }
        total += orders.flatMap { it.extra }
            .filter { order -> productList.any { p -> p.productDetailFkid == order.product?.productDetailId } }
            .sumOf { it.subTotal }
        return total
    }

    //use for promo: get real grand total (using actual price, not promo price)
    private fun getGrandTotalRealPrice(): Int {
        var grandTotal = 0
        val log = StringBuilder("\n")
        orders.forEachIndexed { _, order ->
            var tmpGrandTotal =
                if (order.promotion?.pomotionTypeFkid == 6) 0 else order.product?.priceSell.safe() * order.qty
            if (order.isItemVoid && tmpGrandTotal > 0) {
                tmpGrandTotal *= -1
            }
            order.extra.forEachIndexed { _, extra ->
                tmpGrandTotal += extra.subTotal
            }
            var disc = calculateDiscount(order)
            if (!order.isItemVoid) disc *= -1
            else if (order.isItemVoid && disc < 0) disc *= -1
            log.appendnl("[cal] void: ${order.isItemVoid} | disc: $disc | ${order.product?.name} (${order.qty}) | ${order.subTotal}")
            grandTotal += tmpGrandTotal + disc
        }
        if (grandTotal < 0) grandTotal = 0
        return grandTotal
    }

    fun calculateGrandTotal(isCalculateTax: Boolean = true, isAddRoundUp: Boolean = true): Int {
        var grandTotal = 0
        val log = StringBuilder("calculateGrandTotal :\n")
        orders.forEachIndexed { _, order ->
            var tmpGrandTotal = order.subTotal
            order.extra.forEachIndexed { _, extra ->
                tmpGrandTotal += extra.subTotal
            }
            var disc = calculateDiscount(order)
            if (!order.isItemVoid) disc *= -1
            else if (order.isItemVoid && disc < 0) disc *= -1
//            log.appendLine("[cal] void: ${order.isItemVoid} | disc: $disc | ${order.product?.name} (${order.qty}) | ${order.subTotal}")
            grandTotal += tmpGrandTotal + disc
        }

        log.appendLine("[cal] Grand total without Tax : $grandTotal")

        if (isCalculateTax) {
            var (grandTotalWithDisc, grandTotalWithVcr) = if (discount.discount > 0 || discount.voucher > 0) getTotalWithDiscAndVoucher() else Pair(
                0,
                0
            )
            log.appendLine("[cal] calculateGrandTotal >> (${discount.discount}|${discount.voucher}) grandTotalCanBeDisc : $grandTotalWithDisc | grandTotalCanBeVcr : $grandTotalWithVcr")

            var allDisc = 0
            if (discount.discount > 0) {
                var total = if (discount.discountType == Constant.TYPE_NOMINAL) {
                    if (grandTotalWithDisc < discount.discount) grandTotalWithDisc else discount.discount
                } else ((grandTotalWithDisc * discount.discount) / 100)
                discount.discountNominal = total
                allDisc += total
                log.appendLine("[cal] Discount Value : ${discount.discount} | Type : ${discount.discountType} | Total : $total")
            } else {
                discount.discountNominal = 0
            }

            if (discount.voucher > 0) {
                var total = if (discount.voucherType == Constant.TYPE_NOMINAL) {
                    if (grandTotalWithVcr < discount.voucher) grandTotalWithVcr else discount.voucher
                } else ((grandTotalWithVcr * discount.voucher) / 100)
                discount.voucherNominal = total
                allDisc += total
                log.appendLine("[cal] Voucher Value : ${discount.voucher} | Type : ${discount.voucherType} | Total : $total")
            } else {
                discount.voucherNominal = 0
            }

            promoApplied.filter { it.minOrder <= grandTotal && it.discountType != Constant.PROMO_DISC_TYPE_ITEM }
                .forEach { promo ->
                    val subtotal = grandTotal
                    val total =
                        if (promo.promoDiscountType == "percent") (subtotal * promo.value.safeToInt() / 100).max(
                            promo.promoDiscountMaximum,
                            promo.promoDiscountMaximum > 0
                        ) else promo.value.safeToInt()
                    log.appendLine("[cal] alldisc from promoApplied: $total")
                    allDisc += total
                }

            taxSaleList.filter { it.identifierType == Constant.TAX_TYPE_PROMOTION }.forEach { tax ->
                promotionList.firstOrNull { it.promotionId == tax.identifierId.safeToInt() }
                    ?.let { promo ->
//                        val subtotalProduct = getTotalWithPromotion(promo.promotionProduct)
                        val subtotalProduct = grandTotal
                        var max =
                            if (promo.maximumDiscountNominal in 1 until subtotalProduct) promo.maximumDiscountNominal else subtotalProduct
                        val total =
                            if (tax.type == Constant.TYPE_NOMINAL) tax.total.max(max) else (subtotalProduct * tax.value / 100).max(
                                max
                            )
                        log.appendLine("subtotal promo '${promo.name}' = $subtotalProduct | result : $total | max : ${promo.maximumDiscountNominal}")
                        allDisc += total
                    }
            }

            log.appendLine("[cal] All Disc : $allDisc")
            if (allDisc < 0) {
                allDisc = 0
                log.appendLine("[cal] disc is minus, reset to zero")
            }

            grandTotal -= allDisc
            log.appendLine("[cal] Grand Total after Disc : $grandTotal")

            if (grandTotal > 0) {
                taxSaleList.filter { it.id != null }.forEachIndexed { _, tax ->
                    var total =
                        if (tax.type == Constant.TYPE_NOMINAL) tax.total else (tax.value * grandTotal / 100)
                    tax.total = total
                    if (tax.category?.lowercase() == Constant.TAX_CATEGORY_DISC || tax.category?.lowercase() == Constant.TAX_CATEGORY_VOUCHER) {
                        grandTotal -= total
                    } else {
                        grandTotal += total
                    }
                }
            }
        }

        log.appendLine("Grand Total After Tax : $grandTotal")
        log.appendLine("pembulatan: ${(100 - grandTotal % 100)}")
        if (grandTotal < 0) grandTotal = 0
        if (isAddRoundUp && grandTotal % 100 > 0) grandTotal += (100 - grandTotal % 100)
        if (orders.isEmpty()) grandTotal = 0

        log.appendLine("Grand total final : $grandTotal")
        Timber.i(log.toString())
        return grandTotal
    }

    fun updatePrice(order: Order, holder: GlobalViewHolder<ListItemBillBinding>? = null): Boolean {
        var isNeedToRecalculateTax = false
        val qty = order.qty
        var total = 0
        var price = 0

        //if order is as promo (like free item or special price), use the default price
        Timber.d("[promo] orderPromo: ${order.promotion}")
        if (order.promotion != null) {
            Timber.d("[promo] use default price... ${order.price} -> ${order.promotion}")
            var isPromoApplicable = true
            if (order.promotion?.maxQtyPromo.safe() > 0) {
                val qtyAdded =
                    orders.filter { p -> p.promotion?.promotionId == order.promotion?.promotionId }
                        .sumOf { it.qty }
                isPromoApplicable = qtyAdded <= order.promotion?.maxQtyPromo.safe()
            }

            if (isPromoApplicable) {
                price = order.price
                order.promotion?.promotionValue =
                    (order.product?.priceSell.safe() * qty) - (price * qty)
                if (order.isItemVoid) {
                    order.promotion?.promotionValue = order.promotion?.promotionValue.safe() * -1
                }
                val promoValue =
                    if (order.isItemVoid) abs(order.promotion?.promotionValue.safe()) else abs(order.promotion?.promotionValue.safe()) * -1
                order.info = "(promo ${promoValue.toCurrency()})"
            } else {
                Timber.d("[promo] promo is no longer applicable")
                price = order.product?.priceSell.safe()
            }
        } else {
            price = order.product?.priceSell.safe()
            val multiplePrice = multiplePriceList.sortedWith(compareBy { it.qty })
                .lastOrNull { it.productDetailFkid == order.product?.productDetailId && qty >= it.qty }
            multiplePrice?.let {
                Timber.i("Use Multiple Price. QTY : $qty | Price : ${it.price}")
                order.info = "(use multiple price setting)"
                price = it.price
                if (it.qty == qty)
                    isNeedToRecalculateTax = true
            }

            val grandTotal = getGrandTotalRealPrice()
            var isUsePromo = false
            promoApplied.firstOrNull()?.let { promotion ->
                when (promotion.type) {
                    "special_price" -> {
                        promotion.promotionDetail?.products?.firstOrNull { it.productDetailFkid == order.product?.productDetailId && it.type != Constant.PROMO_PRODUCT_ORDER }
                            ?.let { promotionProduct ->
                                var isPromoApplicable = isPromoQtyStillAvailable(promotion, order)
                                if (isPromoApplicable) {
                                    isPromoApplicable = isPromoTermFulfilled(promotion.toEntity())
                                }

                                if (isPromoApplicable) {
                                    if (promotion.minOrder <= grandTotal) {
                                        price = promotionProduct.price
                                        order.promotion = promotion.simplify()
                                        order.promotion?.promotionValue =
                                            (order.product?.priceSell.safe() * qty) - (price * qty)
                                        order.info = "(promo -${order.promotion?.promotionValue})"
                                        order.grandTotalWatcher =
                                            GrandTotalWatcher(LOWER_THAN, promotion.minOrder)
                                        isUsePromo = true
                                    } else {
                                        order.grandTotalWatcher =
                                            GrandTotalWatcher(GREATER_THAN, promotion.minOrder)
                                    }
                                }
                            }
                    }

                    "discount" -> {
                        Timber.d("[promo] discount: ${Gson().toJson(promotion)}")
                        promotion.promotionDetail?.products?.takeIf { promotion.discountType != Constant.PROMO_DISC_TYPE_NOTA }
                            ?.firstOrNull { it.productDetailFkid == order.product?.productDetailId && it.type != Constant.PROMO_PRODUCT_ORDER }
                            ?.let { promotionProduct ->
                                if (promotion.minOrder <= grandTotal) {
                                    var isPromoApplicable =
                                        isPromoQtyStillAvailable(promotion, order)
                                    isPromoApplicable = if (isPromoApplicable) isPromoTermFulfilled(
                                        promotion.toEntity(),
                                        order
                                    ) else false

                                    if (isPromoApplicable) {
                                        Timber.d("[promo] add promo: $promotionProduct")
                                        val discount =
                                            if (promotionProduct.isPercent == 1) ((promotionProduct.amount.toFloat() / 100.0) * price).toInt() else promotionProduct.amount
                                        price -= discount
                                        order.promotion = promotion.simplify()
                                        order.promotion?.promotionValue =
                                            (order.product?.priceSell.safe() * qty) - (price * qty)
                                        order.info = "(promo -${order.promotion?.promotionValue})"
                                        order.grandTotalWatcher =
                                            GrandTotalWatcher(LOWER_THAN, promotion.minOrder)
                                        isUsePromo = true
                                    }
                                } else {
                                    order.grandTotalWatcher =
                                        GrandTotalWatcher(GREATER_THAN, promotion.minOrder)
                                }
                            }
                    }

                    else -> {
                    }
                }

                order.promotion?.let {
                    order.promotion?.parentId = getParentIdsOfPromo(promotion.toEntity(), order)
                }
            }

            if (!isUsePromo) {
                val promoInRange = promotionList.filter { it.isInRange }
                Timber.i("promo in range size : ${promoInRange.size}")
                promoInRange.forEach { promotion ->
                    Timber.d("[promo] promo in range : ${promotion.name} - ${promotion.promotionType}")
                    if (getQtyPromoCanBeAdded(promotion, order) <= 0){
                        return@forEach
                    }
//
//                    if (promotion.maxQtyPromo != null) {
//                        val qtyAdded =
//                            orders.filter { p -> p.promotion?.promotionId == promotion.promotionId }
//                                .sumOf { it.qty } + order.qty
//                        if (qtyAdded > promotion.maxQtyPromo.safe()) {
//                            Timber.d("[promo] qty added exceeded, qty: $qtyAdded, max: ${promotion.maxQtyPromo}")
//                            return@forEach
//                        }
//                    }
//
//                    //check qty term,
//                    //if multiples apply (berlaku kelipatan),
//                    if (!isPromoTermFulfilled(promotion, order)) {
//                        Timber.d("skip... no isPromoTermFulfilled")
//                        return@forEach
//                    }

                    val isMemberEligible = isMemberEligiblePromotion(promotion)
                    when {
                        promotion.promotionType.equalIgnoreCase(Constant.PROMO_SPECIAL_PRICE) || (promotion.promotionType.equalIgnoreCase(
                            Constant.PROMO_SPECIAL_PRICE_MEMBER
                        ) && isMemberEligible) -> {
                            promotion.promotionProduct.firstOrNull { it.productDetailId == order.product?.productDetailId && it.type != Constant.PROMO_PRODUCT_ORDER }
                                ?.let { promotionProduct ->
                                    if (promotion.minOrder <= grandTotal) {
                                        Timber.i("[promo] Found Special Price ${promotionProduct.price} | id : ${promotion.promotionId} | minOrder : ${promotion.minOrder}")
                                        price = promotionProduct.price ?: price
                                        val infoAdd = if (isMemberEligible) "member" else ""
//                                        order.info = "(special price $infoAdd applied)"
                                        order.promotion = promotion.simplify()
                                        order.promotion?.promotionValue =
                                            (order.product?.priceSell.safe() * qty) - (price * qty)
                                        order.info =
                                            "(promo -${order.promotion?.promotionValue?.toCurrency()})"
                                        if (promotion.minOrder > 0)
                                            order.grandTotalWatcher =
                                                GrandTotalWatcher(LOWER_THAN, promotion.minOrder)
                                    } else {
                                        Timber.i("[promo] grandTotal of '${order.product?.name}' not meet min expectation. require ${promotion.minOrder} | current $grandTotal")
                                        order.grandTotalWatcher =
                                            GrandTotalWatcher(GREATER_THAN, promotion.minOrder)
                                    }
                                }
                        }

                        (promotion.promotionType.equalIgnoreCase(Constant.PROMO_DISCOUNT) || (promotion.promotionType.equalIgnoreCase(
                            Constant.PROMO_DISCOUNT_MEMBER
                        ) && isMemberEligible)) && promotion.discountType == Constant.PROMO_DISC_TYPE_ITEM -> {
                            Timber.d("[promo] discount:  ${Gson().toJson(promotion)}")
                            promotion.promotionProduct.firstOrNull { it.productDetailId == order.product?.productDetailId && it.type != Constant.PROMO_PRODUCT_ORDER }
                                ?.let { promotionProduct ->
                                    if (promotion.minOrder <= grandTotal) {
                                        Timber.i("[promo] Found Promo Discount ${promotionProduct.ammount} | isPrice ${promotionProduct.isPercent} | id : ${promotion.promotionId} (${promotion.name})")
                                        val discount =
                                            if (promotionProduct.isPercent == 1) ((promotionProduct.ammount.toFloat() / 100.0) * price).toInt() else promotionProduct.ammount
                                        price -= discount
//                                        val infoAdd = if (isMemberEligible) "member" else ""
//                                        order.info = "(promo discount $infoAdd applied)"
                                        order.promotion = promotion.simplify()
                                        order.promotion?.discount = discount
                                        order.promotion?.promotionValue =
                                            (order.product?.priceSell.safe() * qty) - (price * qty)
                                        order.info =
                                            "(promo -${order.promotion?.promotionValue.toCurrency()})"

                                        if (promotion.minOrder > 0)
                                            order.grandTotalWatcher =
                                                GrandTotalWatcher(LOWER_THAN, promotion.minOrder)
                                    } else {
                                        Timber.i("[promo] grandTotal of '${order.product?.name}' not meet min expectation. require ${promotion.minOrder} | current $grandTotal")
                                        order.grandTotalWatcher =
                                            GrandTotalWatcher(GREATER_THAN, promotion.minOrder)
                                    }
                                }
                        }
                    }

                    order.promotion?.let {
                        order.promotion?.parentId = getParentIdsOfPromo(promotion)
                    }
                }
            }

            order.promotion?.let { promoUsed ->
                _toastMessage.postValue(
                    Event(
                        ToastMessage(
                            "promo \"${promoUsed.name.uppercase()}\" berhasil digunakan",
                            Level.WARNING
                        )
                    )
                )
            }
        }

        total = (price * qty)
        if (order.isItemVoid && price > 0) {
            price *= -1
            total *= -1
        }

        holder?.binding?.txtPrice?.text = total.toCurrency()
        order.price = price
        order.subTotal = total
        Timber.i("[UP] ${order.product?.name} ($price) $qty  $total |${order.info}| |${order.promotion?.promotionValue}|  -> void? ${order.isItemVoid}")

        //if the result it has not to recalculate tax, check if current disc & voucher bill is nominal,
        //because, when it is, then tax should be re-calculated
        if (!isNeedToRecalculateTax) {
            isNeedToRecalculateTax =
                (discount.discountNominal > 0 && discount.discountType == Constant.TYPE_NOMINAL) || (discount.voucherNominal > 0 && discount.voucherType == Constant.TYPE_NOMINAL)
        }

        return isNeedToRecalculateTax
    }

    private fun getParentIdsOfPromo(promotion: PromotionEntity, order: Order? = null): String? {
        val productTerms =
            promotion.promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
        if (productTerms.isNotEmpty()) {
            val parentIds =
                orders.filter { o -> o.tmpId != order?.tmpId && productTerms.any { p -> p.productDetailId == o.product?.productDetailId } }
                    .map { it.tmpId }.joinToString()
            Timber.d("[promo] parentId: ${parentIds} | ${promotion.name}")
            return parentIds
        }
        return null
    }

    fun getAdapterCategory(
        productsTmp: ArrayList<ProductEntity>,
        type: CategoryType
    ): GlobalAdapter<ListItemCategoryBinding> {
        return object :
            GlobalAdapter<ListItemCategoryBinding>(R.layout.list_item_category, categories) {
            override fun onBindViewHolder(
                holder: GlobalViewHolder<ListItemCategoryBinding>,
                position: Int
            ) {
                super.onBindViewHolder(holder, position)
//                var v = holder.itemView
                if ((position == selectedCategoryPosition && type == CategoryType.MAIN) || (position == selectedCategoryExtraPosition && type == CategoryType.EXTRA)) {
                    holder.binding.layoutList.setBackgroundColor(Color.parseColor("#353F4C"))
                    holder.binding.categoryName.setTextColor(
                        Utils.getColor(
                            holder.itemView.context,
                            R.color.text_orange
                        )
                    )

                    productsTmp.clear()
                    val tmpId = if (type == CategoryType.MAIN) {
                        selectedCategoryId = categories[position].productCategoryId
                        selectedCategoryId
                    } else {
                        selectedCategoryExtraId = categories[position].productCategoryId
                        selectedCategoryExtraId
                    }

                    if (tmpId < 0) productsTmp.addAll(productsUnique)
                    else productsTmp.addAll(productsUnique.filter { p -> p.productSubcategoryFkid == tmpId })
                } else {
                    holder.binding.layoutList.setBackgroundColor(0)
                    holder.binding.categoryName.setTextColor(
                        Utils.getColor(
                            holder.itemView.context,
                            R.color.text_default
                        )
                    )
                }
            }
        }
    }

    fun generatePrintFormat(
        sales: SalesEntity, outlet: Outlet, employee: Employee, isOnlyPrintKitchen: Boolean = false,
        isCloseAfterPrint: Boolean = false, isOpenCashDrawer: Boolean = false
    ) {
        viewModelScope.launch {
            val printers = settingRepository.getPrinterList()
            Timber.i("printer size : ${printers.size}")
            val ticketList = settingRepository.getPrinterTicketOrders()
            val printDataList = NotaManager.createNota(
                printers,
                ticketList,
                sales,
                outlet,
                employee,
                isOnlyPrintKitchen,
                isOpenCashDrawer
            )
            printTask.postValue(Pair(printDataList, isCloseAfterPrint))
        }
    }

    fun getUsedTaxes(): ArrayList<TaxEntity> {
        val list = ArrayList<TaxEntity>()
        orders.forEach { order ->
            order.product?.let { p ->
                list.addAll(taxes.filter { it.productDetailFkid == p.productDetailId && it.dataStatus == "on" && !list.any { l -> l.taxFkid == it.taxFkid } })
            }
            order.extra?.forEach { extra ->
                extra.product?.let { p ->
                    list.addAll(taxes.filter { it.productDetailFkid == p.productDetailId && it.dataStatus == "on" && !list.any { l -> l.taxFkid == it.taxFkid } })
                }
            }
        }
        return list
    }

    fun updateTax(order: Order, discount: Int = 0, isAdd: Boolean = true, qty: Int = 1) {
        var log = StringBuilder()
        log.appendnl("\n#TAX update tax of ${order.product?.name} (${order.product?.productDetailId}). |void: ${order.isItemVoid} | disc : '$discount' qty = $qty. price: ${order.price} | isAdd: $isAdd")
        var grandTotalNoTax = -1 //initial value
        var total: Int
        order.product?.let { p ->
            val id = p.productDetailId
            val tax = taxes.filter { it.productDetailFkid == id && it.dataStatus == "on" }
                .distinctBy { it.taxFkid }
            log.appendLine(
                "tax all size ${taxes.size} | found : ${
                    Gson().toJson(tax.flatMap {
                        listOf(
                            "taxFkid: ${it.taxFkid} - taxDetailId: ${it.taxdetailId}"
                        )
                    })
                }"
            )
            tax.forEach { t ->
                val gratuity = gratuities.firstOrNull { it.gratuityId == t.taxFkid }
                gratuity?.let {
                    //val taxSaved = salesEdit?.taxes?.firstOrNull { it.id == gratuity.gratuityId }
                    if (taxesEnable[gratuity.gratuityId] == true || gratuity.taxStatus == "permanent") {
                        var subtotal = order.price * qty
                        total = if (gratuity.taxType == Constant.TYPE_PERSEN) {
                            order.qty.max()
                            // total = (total.div(100))?.times(orders[position].product.productDetail?.get(0)?.price?.toInt() ?: 0)
                            var discAndVoucherPerItem =
                                abs(getDiscOrVoucherPerItem(subtotal, order, discount * qty))
                            log.append("discAndVoucherPerItem (all): $discAndVoucherPerItem, ")
//                            discAndVoucherPerItem = (discAndVoucherPerItem/order.qty.NonZero()) * qty
//                            log.append("discAndVoucherPerItem (final): $discAndVoucherPerItem, ")
                            subtotal = abs(subtotal)
                            val promoPerItem = abs(PromotionUtil(this).getPromotionPerItem(order))
                            log.appendLine("promoPerItem: $promoPerItem, ")

                            log.appendLine(
                                "[TAX] Subtotal : $subtotal | discAndVoucherPerItem : $discAndVoucherPerItem | promoItem: $promoPerItem | Gratuity : ${gratuity.jumlah} (${gratuity.taxType}) | Disc Item : ${
                                    abs(
                                        discount
                                    )
                                } x $qty - allItemQty: ${order.qty}"
                            )

                            log.appendLine("${gratuity.jumlah} / 100 x $subtotal - ${abs(discount)} x $qty - $discAndVoucherPerItem - $promoPerItem")
                            ((gratuity.jumlah / 100f) * ((subtotal - (abs(discount) * qty)) - discAndVoucherPerItem - promoPerItem)).toInt()
                        } else {
                            //harga/jumlah harga total * tax nominal <-- RUMUS
                            if (grandTotalNoTax == -1) {
                                grandTotalNoTax = calculateGrandTotal(false)
                            }

                            log.appendLine("${subtotal.toFloat()} / ${grandTotalNoTax.toFloat()} * ${gratuity.jumlah}")
                            ((subtotal.toFloat() / grandTotalNoTax.toFloat()) * gratuity.jumlah).toInt()
                        }

                        if (!isAdd && total > 0) {
                            total *= -1
                        }

                        log.appendLine(" | Result : $total")

                        val currentTax = order.taxes.firstOrNull { it.id == gratuity.gratuityId }
                        currentTax?.let {
                            it.total += total
                        } ?: kotlin.run {
                            order.taxes.add(
                                TaxSales(
                                    gratuity.gratuityId,
                                    gratuity.name,
                                    total,
                                    gratuity.taxType,
                                    gratuity.jumlah,
                                    gratuity.taxCategory
                                )
                            )
                        }

                        var isFound = false
                        val taxSalesNeedRemoves = ArrayList<TaxSales>()
                        taxSaleList.forEachIndexed { index, taxSales ->
                            if (taxSales.id == gratuity.gratuityId) {
                                isFound = true
                                if (gratuity.taxType == Constant.TYPE_PERSEN) {
                                    val tmpTax = taxSaleList[index]
                                    tmpTax.type = Constant.TYPE_NOMINAL
                                    val currentTax = tmpTax.total
                                    tmpTax.total += total
                                    log.appendLine("[TAX] Current Tax of '${gratuity.name}' is $currentTax | total new $total | now ${tmpTax.total} | isAdd $isAdd \n")
                                    if (tmpTax.total < 0) tmpTax.total = 0
                                } else {
                                    //if tax is nominal, and not being used by any order, then remove it
                                    if (order.qty <= 0 || grandTotalNoTax == 0) {
                                        val excludeOrder =
                                            orders.filter { it.tmpId != order.tmpId && !it.isItemVoid }
                                        val useByOrder = excludeOrder.flatMap { it.taxes }
                                            .filter { it.id == gratuity.gratuityId }
                                        val usedByExtra =
                                            excludeOrder.flatMap { it.extra }.flatMap { it.taxes }
                                                .filter { it.id == gratuity.gratuityId }
                                        log.appendLine("not useByOrder: ${useByOrder.isEmpty()} | not usedByExtra: ${usedByExtra.isEmpty()} | ${order.tmpId} ${excludeOrder.map { it.tmpId }}")
                                        if ((useByOrder.isEmpty() && usedByExtra.isEmpty()) || grandTotalNoTax == 0) {
                                            log.appendLine("tax '${gratuity.name}' should be removed...")
                                            taxSalesNeedRemoves.add(taxSales)
//                                            taxSaleList.remove(taxSales)
                                        }
                                    }
                                }
                                return@forEachIndexed
                            }
                        }

//                        taxSalesNeedRemoves.forEach { tax -> taxSaleList.remove(tax) }
                        taxSaleList.removeAll(taxSalesNeedRemoves.toSet())

                        if (!isFound && total > 0) {
                            val realTotal =
                                if (gratuity.taxType == Constant.TYPE_PERSEN) total else gratuity.jumlah
                            log.appendLine("[TAX] Added new Tax.'${gratuity.name}' Total $realTotal (${gratuity.taxType}) \n")
                            taxSaleList.add(
                                TaxSales(
                                    gratuity.gratuityId,
                                    gratuity.name,
                                    realTotal, /*gratuity.taxType*/
                                    Constant.TYPE_NOMINAL,
                                    gratuity.jumlah,
                                    gratuity.taxCategory
                                )
                            )
                        } else {
                        }
                    } else {
                        log.appendLine("tag '${gratuity.name}' (${gratuity.gratuityId}) is disabled")
                    }
                } ?: run { log.appendLine("gratuity with id '${t.taxFkid}' not found!") }
            }
        } ?: run { log.appendLine("WARN... product is null. can't update tax") }
        Timber.i(log.toString())
    }

    //rumus => harga/jumlah harga total * discount nominal
    private fun getDiscOrVoucherPerItem(price: Int, order: Order, discItem: Int = 0): Int {
        var result = 0
        val (grandTotalWithDisc, grandTotalWithVcr) = getTotalWithDiscAndVoucher()
        if (discount.discount > 0 && order.product?.discount == "on") {
            if (discount.discountType == Constant.TYPE_NOMINAL) {
                result += ((price.toFloat() - discItem) / grandTotalWithDisc.toFloat() * discount.discount).toInt()
                Timber.i(">> DISC : Price : $price | grandTotalWithDisc : $grandTotalWithDisc | Discount : ${discount.discount} | Result : $result")
            } else {
                result += ((discount.discount.toDouble()/100.0) * (price-discItem).toDouble()).toInt()
                Timber.i(">> DISC : Result : $result | Discount : ${discount.discount} (${discount.discountType}) | discItem: $discItem | subTotal: ${order.subTotal} | Price : $price | grandTotalWithDisc : $grandTotalWithDisc")
            }
        }

        if (discount.voucher > 0 && order.product?.voucher == "on") {
            if (discount.voucherType == Constant.TYPE_NOMINAL) {
                result += ((price.toFloat() - discItem) / grandTotalWithVcr.toFloat() * discount.voucher).toInt()
                Timber.i(">> VOUCHER : Price : $price | grandTotalWithVcr : $grandTotalWithVcr | Voucher : ${discount.voucher} | Result : $result | type : ${discount.voucherType}")
            } else {
//                result += ((discount.voucher * order.subTotal).toDouble() / 100f).toInt()
                result += ((discount.voucher.toDouble()/100.0) * (price-discItem).toDouble()).toInt()
                Timber.i(">> VOUCHER : Result : $result | Voucher : ${discount.voucher} (${discount.voucherType}) | Price : $price | grandTotalWithVcr : $grandTotalWithVcr")
            }
        }

        return result
    }

    /**
     * oke
     * @return Get nominal discount per item
     */
    fun calculateDiscount(order: Order): Int {
        return if (order.discount.discount > 0) {
            if (order.discount.discountType == Constant.TYPE_PERSEN) {
                val totalExtra = order.extra?.sumOf { it.subTotal } ?: 0
                val subtotal = order.subTotal + totalExtra
                val discount = (order.discount.discount.toDouble() / 100) * subtotal
                discount.toInt()
            } else order.discount.discount
        } else 0
    }

    fun calculateDiscPerItem(order: Order, mainOrder: Order, isChange: Boolean = false): Int {
        return if (mainOrder.discount.discount > 0) {
            if (mainOrder.discount.discountType == Constant.TYPE_PERSEN)
                (mainOrder.discount.discount * order.price / 100)
            else if (mainOrder.discount.discountType == Constant.TYPE_NOMINAL && !isChange) {
//                rumus => subtotal/jumlah subtotal * discount nominal
                var totalSubtotal = mainOrder.subTotal + (mainOrder.extra?.sumOf { it.subTotal }
                    ?: 0)
                (((order.subTotal.toFloat() / totalSubtotal.toFloat()) * mainOrder.discount.discount) / order.qty).toInt()
//                mainOrder.discount.discount
            } else {
                0
            }
        } else 0
    }

    fun filterCategory() {
        categories.clear()
        categoriesTmp.filter { it.productCategoryId < 0 }.forEach { category ->
            if (category.productCategoryId == Constant.MenuCategoryPromo) {
                if (promotionList.isNotEmpty() && BuildConfig.DEBUG) {
//                    categories.add(category)
                }
            } else {
                categories.add(category)
            }
        }

        Timber.i(">>PRODUCT total product: ${products.size}")
        if (products.isNotEmpty()) {
            categoriesTmp.filter { p -> products.any { it.productSubcategoryFkid == p.productCategoryId } }
                .let { items ->
                    categories.addAll(items)
                }
        }

        //sort category by position
        categories.sortBy { it.position }
    }

    fun reCalculateTax() {
        Timber.i(">>> recalculate tax...")
        taxSaleList.clear()
        addSalesTaxPromotion()

        orders.forEach { order ->
            var disc = calculateDiscPerItem(order, order)
            Timber.i("Disc peritem : $disc")
            order.taxes.clear()

            updateTax(order, disc, !order.isItemVoid, order.qty)
            order.extra.forEach {
                it.taxes.clear()
                disc = calculateDiscPerItem(it, order)
                updateTax(it, disc, !order.isItemVoid, it.qty)
            }
        }

        addSalesTaxDiscVoucher()
        taxSaleList.sortBy { it.id }
    }

    private fun addSalesTaxPromotion() {
        val grandTotal = calculateGrandTotal(false, false)
        promoApplied.firstOrNull { it.minOrder <= grandTotal }?.let { promotion ->
            if (promotion.discountType == Constant.PROMO_DISC_TYPE_NOTA) {
                //if promotion has term product, check it
//                val subtotal =
//                    promotionon.promotionDetail?.products?.let { getTotalWithPromotions(it) }.safe()
                var isApplicable = isPromoTermFulfilled(promotion.toEntity())
                val parentIds = getParentIdsOfPromo(promotion.toEntity())

                if (isApplicable) {
                    val total =
                        if (promotion.promoDiscountType == "percent") (grandTotal * promotion.value.safeToInt() / 100).max(
                            promotion.promoDiscountMaximum,
                            promotion.promoDiscountMaximum > 0
                        ) else promotion.value.safeToInt().max(grandTotal)
                    val type =
                        if (promotion.promoDiscountType == "percent") Constant.TYPE_PERSEN else Constant.TYPE_NOMINAL
                    taxSaleList.add(
                        TaxSales(
                            null,
                            formatPromoName(promotion.name),
                            total,
                            type,
                            promotion.value.safeToInt(),
                            Constant.TAX_CATEGORY_VOUCHER,
                            identifierId = promotion.code,
                            identifierType = Constant.TAX_TYPE_PROMOTION,
                            parentIds = parentIds
                        )
                    )
                    promotion.promoNominal = total
                    promotion.promotionValue = total
                }
            } else if (promotion.promoDiscountType == "percent" && promotion.typeId == Constant.PROMO_TYPE_DEALS) {
                var isApplicable = isPromoTermFulfilled(promotion.toEntity())
                if (isApplicable) {
                    val parentIds = getParentIdsOfPromo(promotion.toEntity())
                    val total = (grandTotal * promotion.value.safeToInt() / 100).max(
                        promotion.promoDiscountMaximum,
                        promotion.promoDiscountMaximum > 0
                    )
                    taxSaleList.add(
                        TaxSales(
                            null,
                            formatPromoName(promotion.name),
                            total,
                            Constant.TYPE_PERSEN,
                            promotion.value.safeToInt(),
                            Constant.TAX_CATEGORY_VOUCHER,
                            identifierId = promotion.code,
                            identifierType = Constant.TAX_TYPE_PROMOTION,
                            parentIds = parentIds
                        )
                    )
                    promotion.promoNominal = total
                    promotion.promotionValue = total
                    Timber.i("add this promo to tax list (percent) : $promotion")
                }
            } else if (promotion.type == "nominal" || promotion.type == "voucher" || (promotion.promoDiscountType == Constant.TYPE_NOMINAL && promotion.typeId == Constant.PROMO_TYPE_DEALS)) {
                var isApplicable = isPromoTermFulfilled(promotion.toEntity())
                if (isApplicable) {
                    val parentIds = getParentIdsOfPromo(promotion.toEntity())
                    taxSaleList.add(
                        TaxSales(
                            null,
                            formatPromoName(promotion.name),
                            promotion.value.safeToInt(),
                            promotion.promoDiscountType
                                ?: Constant.TYPE_NOMINAL,
                            promotion.value.safeToInt(),
                            Constant.TAX_CATEGORY_VOUCHER,
                            identifierId = promotion.code,
                            identifierType = Constant.TAX_TYPE_PROMOTION,
                            parentIds = parentIds
                        )
                    )
                    promotion.promotionValue = promotion.value.safeToInt()
                    Timber.i("add this promo to tax list : $promotion")
                }
            }
        }

        promotionList
            .filter { it.minOrder <= grandTotal && it.isInRange }
            .filter { it.discountType == Constant.PROMO_DISC_TYPE_NOTA }
            .filter { promo ->
                promo.promotionType.equalIgnoreCase(Constant.PROMO_DISCOUNT) || (memberDetail?.memberId.safe()
                    .isNotEmpty() && promo.promotionType.equalIgnoreCase(
                    Constant.PROMO_DISCOUNT_MEMBER
                ) && isMemberEligiblePromotion(promo))
            }
            .takeIf { it.isNotEmpty() }?.let { promotions ->
                promotions.forEach { promo ->
//                    val subtotalPromo = getTotalWithPromotion(promo.promotionProduct)
                    var isTermFulfilled = isPromoTermFulfilled(promo)
                    val subtotalPromo = grandTotal
                    if (subtotalPromo > 0 && isTermFulfilled) {
                        val total =
                            if (promo.usePercent == 1) (subtotalPromo * promo.ammount.safeToInt() / 100).max(
                                promo.maximumDiscountNominal,
                                promo.maximumDiscountNominal > 0
                            ) else promo.ammount.safeToInt().max(subtotalPromo)
                        taxSaleList.add(
                            TaxSales(
                                null,
                                promo.name,
                                total,
                                if (promo.usePercent == 1) Constant.TYPE_PERSEN else Constant.TYPE_NOMINAL,
                                promo.ammount.safeToInt(),
                                Constant.TAX_CATEGORY_DISC,
                                identifierId = promo.promotionId.toString(),
                                identifierType = Constant.TAX_TYPE_PROMOTION
                            )
                        )
                    }
                }
            }
    }

    private fun addSalesTaxDiscVoucher() {
        val (grandTotalWithDisc, grandTotalWithVcr) = getTotalWithDiscAndVoucher()
        Timber.i("[DISC] : ${discount.discount} | Voucher : ${discount.voucher} | grandTotalWithDisc : $grandTotalWithDisc | grandTotalWithVcr : $grandTotalWithVcr")
        if (discount.discount > 0) {
            val total = if (discount.discountType == Constant.TYPE_NOMINAL) {
                if (grandTotalWithDisc < discount.discount) grandTotalWithDisc else discount.discount
            } else {
                grandTotalWithDisc * discount.discount / 100
            }
            taxSaleList.add(
                TaxSales(
                    null,
                    "Discount",
                    total,
                    discount.discountType,
                    discount.discount,
                    Constant.TAX_CATEGORY_DISC
                )
            )
        }

        if (discount.voucher > 0) {
            val total = if (discount.voucherType == Constant.TYPE_NOMINAL) {
                if (grandTotalWithVcr < discount.voucher) grandTotalWithVcr else discount.voucher
            } else {
                grandTotalWithVcr * discount.voucher / 100
            }
            taxSaleList.add(
                TaxSales(
                    null,
                    "Voucher",
                    total,
                    discount.voucherType,
                    discount.voucher,
                    Constant.TAX_CATEGORY_VOUCHER
                )
            )
        }
    }

    private fun isPromoQtyStillAvailable(promotion: Promotion, order: Order): Boolean {
        var isPromoApplicable = true
        Timber.d("[promo] maxQty: ${promotion.maxQtyPromo} | ${promotion.name} | ${promotion.code}")
        if (promotion.maxQtyPromo != null) {
            val qtyAdded =
                orders.filter { p -> p.promotion?.promotionCode == promotion.code }
                    .sumOf { it.qty } + order.qty
            Timber.d("[promo] maxQty, added: $qtyAdded")
            isPromoApplicable = qtyAdded <= promotion.maxQtyPromo.safe()
        }
        return isPromoApplicable
    }

    fun isPromoTermFulfilled(promo: PromotionEntity, order: Order? = null): Boolean {
        var fulfilled = true
        val productTerm = promo.promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
        if (productTerm.isNotEmpty()) {
            val qtyTerm =
                orders.filter { p -> productTerm.any { term -> term.productDetailId == p.product?.productDetailId } }
                    .filter { p -> p.tmpId != order?.tmpId.safe() }
                    .sumOf { it.qty }
            fulfilled = qtyTerm >= productTerm.first().qty
            Timber.i("[promo] term, in bill: $qtyTerm | should: ${productTerm.first().qty} | $fulfilled | ${promo.name}")
        } else {
            Timber.d("[promo] term: no product should order | ${promo.name} | ${promo.promoType}")
        }
        return fulfilled
    }

    fun getQtyPromoCanBeAdded(promo: PromotionEntity, order: Order? = null): Int {
        val productTerm = promo.promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
        val qtyAdded =
            orders.filter { p -> p.promotion?.promotionId == promo.promotionId }
                .sumOf { it.qty } //+ order?.qty.safe()
        //possibilities:
        //no term: means only check max qty
        //with terms: check the term first, then check if multiples apply & maxQty set
        // -> if multipleApply, use that as base
        // -> if no multipleApply,

        //check if terms fulfilled
        if (productTerm.isNotEmpty()) {
            val qtyTermAdded =
                orders.filter { p -> productTerm.any { term -> term.productDetailId == p.product?.productDetailId } }
                    .filter { p -> p.tmpId != order?.tmpId.safe() }
                    .sumOf { it.qty }
            if(qtyTermAdded < productTerm.first().qty){
                Timber.i("[promo] term not fullfilled, in bill: $qtyTermAdded | should: ${productTerm.first().qty} | ${promo.name}")
                return 0
            }

            var qty = promo.maxQtyPromo.safe(default = 1)

            //can be added = termsAdded/qtyTerm * maxQty
            promo.termsMultipleApply?.takeIf { promo.maxQtyPromo.safe() > 0 && it > 0 }?.let {
                qty = (qtyTermAdded/productTerm.first().qty) * promo.maxQtyPromo.safe()
                Timber.d("[promo] (termsMultipleApply) qtyTermAdded: $qtyTermAdded, terms: ${productTerm.first().qty}, maxQty: ${promo.maxQtyPromo}")
            }
            qty -= qtyAdded
            Timber.d("[promo] qty can added: $qty, qtyAdded: $qtyAdded")
            return qty
        } else if(promo.maxQtyPromo.safe() > 0) {
            if (qtyAdded >= promo.maxQtyPromo.safe()){
                Timber.d("[promo] qty added exceeded, qty: $qtyAdded, max: ${promo.maxQtyPromo} | ${promo.name}")
                return 0
            }
        }
        return 1
    }

    //because disc & voucher will be named respectively,
    //to avoid confuse, let's make other different
    private fun formatPromoName(promoName: String?): String {
        return if (promoName == "Discount" || promoName == "Voucher") {
            "#$promoName"
        } else {
            promoName ?: "[NO NAME]"
        }
    }

    fun recalculateDiscAndVoucher() {
        // val grandTotal = calculateGrandTotal(false)
        if (discount.discount > 0 || discount.voucher > 0) {
            val (grandTotalWithDisc, grandTotalWithVcr) = getTotalWithDiscAndVoucher()
            Timber.i("Disc : ${discount.discount} | Voucher : ${discount.voucher}")
            if (discount.discount > 0) {
                val total = if (discount.discountType == Constant.TYPE_NOMINAL) {
                    if (grandTotalWithDisc < discount.discount) grandTotalWithDisc else discount.discount
                } else {
                    grandTotalWithDisc * discount.discount / 100
                }
                taxSaleList.firstOrNull { it.id == null && it.name == "Discount" }?.let {
                    it.total = total
                }
            }

            if (discount.voucher > 0) {
                val total = if (discount.voucherType == Constant.TYPE_NOMINAL) {
                    if (grandTotalWithVcr < discount.voucher) grandTotalWithVcr else discount.voucher
                } else {
                    grandTotalWithVcr * discount.voucher / 100
                }
                taxSaleList.firstOrNull { it.id == null && it.name == "Voucher" }?.let {
                    it.total = total
                }
            }
        }

        if (promoApplied.isNotEmpty()) {
            taxSaleList.firstOrNull { it.identifierType == "promotion" && it.type != Constant.TYPE_NOMINAL }
                ?.let { tax ->
                    promoApplied.firstOrNull { it.code == tax.identifierId }?.let { promo ->
                        var maxDisc = promo.promoDiscountMaximum.safe()
//                        val grandTotal = if (promo.discountType == Constant.PROMO_DISC_TYPE_NOTA) {
//                            maxDisc = 0
//                            promo.promotionDetail?.products?.let { getTotalWithPromotions(it) }
//                                .safe()
//                        } else {
//                            calculateGrandTotal(false)
//                        }

                        val grandTotal = calculateGrandTotal(false)
                        val discValue = tax.value
                        val total =
                            if (tax.type == Constant.TYPE_NOMINAL) discValue else grandTotal * discValue / 100
                        tax.total = total.max(maxDisc, maxDisc > 0)
                        promo.promoNominal = tax.total
                        promo.promotionValue = tax.total
                    }
                }
        }

        if (promotionList.isNotEmpty()) {
            taxSaleList.filter { it.identifierType == "promotion" }.forEach { tax ->
                promotionList.firstOrNull { it.promotionId == tax.identifierId.safeToInt() }
                    ?.let { promo ->
//                        val subtotalPromo = getTotalWithPromotion(promo.promotionProduct)
                        val subtotalPromo = calculateGrandTotal(false)
                        if (tax.type == Constant.TYPE_NOMINAL) {
                            tax.total = tax.value.max(subtotalPromo)
                        } else {
                            tax.total = subtotalPromo * tax.value / 100
                        }
                    }
            }
        }
    }

    fun removeFreeItemByParentId(id: Long) {
        orders.filter { order ->
            order.promotion?.parentId?.split(",")?.any { it.safeToLong() == id } == true
        }.forEach { order ->
            if (order.isHold) {
//                order.price = order.product?.priceSell ?: 0
                order.qty = order.holdQty
                order.promotion = null
                order.info = null
                updatePrice(order)
            } else {
                orders.remove(order)
            }
        }
        taskRefreshBill.call()

        reCalculateTax()
        taskRefreshTax.call()
    }

    //refreshPromo: update bill according to bill
    private fun refreshPromo(isAppliedManual: Boolean = false) {
        //first clear all item gained from promo
        orders.filter { it.promotion?.promotionCode != null && it.promotion?.parentId != null }
            .forEach { order ->
                orders.remove(order)
            }

        promoApplied.firstOrNull()?.let { promo ->
            Timber.i(">> [promo] : ${Gson().toJson(promo)}")
            promo.takeIf { it.type == "free" }?.promotionDetail?.let { promotionVoucher ->
                //if free item only one product, then add it directly to bill
                if (promotionVoucher.productsFree.size == 1) {
                    val freeItem = createPromotionFreeList()
                    val max = freeItem.firstOrNull()?.qtyMax ?: 0
                    freeItem.firstOrNull { it.productDetailFkid != null }?.let { free ->
                        free.qtySelected = max
                    }
                    addFreeItem(freeItem)
                } else {
                    //otherwise, show free item list dialog
                    if (!isAppliedManual)
                        taskShowVoucherDetail.call()
                }
            } ?: run {
//                taskRefreshBill.call()
                Timber.d("[promo] promo applied : ${Gson().toJson(promo)}")
                if (promo.typeId == Constant.PROMO_TYPE_DEALS) {
                    promo.promotionDetail?.products?.forEach { product ->
                        orders.firstOrNull { it.product?.productDetailId == product.productDetailFkid }
                            ?.let { order -> updatePrice(order) }
                    }
                }
                reCalculateTax()
            }
            taskRefreshPromo.call()
        }

        taskRefreshBill.call()
    }

    //for promotion type: free
    fun applyFreeItem(freeItems: List<PromotionFree>) {
//        freeItems.forEach { p -> orders.filter { it.promotion?.promotionId == p.promotionId }.forEach { order -> orders.remove(order) } }

        val freeItemBill = ArrayList<Order>()
        freeItems.filter { it.qtySelected > 0 }.forEach { item ->
            var parentIds = ""
            promotionList.firstOrNull { it.isInRange && it.promotionId == item.promotionId }
                ?.let { promo ->
                    promo.promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
                        .forEach { promoProduct ->
                            orders.filter { it.product?.productDetailId == promoProduct.productDetailId }
                                .forEach { order ->
                                    parentIds += "${order.tmpId},"
                                }
                        }
                }
            Timber.i("adding ${item.productDetailFkid}")

            //promotion code should be set if promo is voucher, so that it will be removed from bill
            //when the voucher removed
            val promotionCode =
                promoApplied.firstOrNull { it.promotionId == item.promotionId && it.type == "free" }?.code

            products.firstOrNull { it.productDetailId == item.productDetailFkid }?.let { product ->
                //val promotionSales = PromotionSales(promotionCode = promo.code, parentId = parentIds, pomotionTypeFkid = promo.typeId)
                val promotionSales = PromotionSales(
                    promotionId = item.promotionId,
                    parentId = parentIds,
                    pomotionTypeFkid = item.promotionTypeId,
                    promotionCode = promotionCode
                )
                val variantName = product.variantFkid?.let { variantId ->
                    variants.firstOrNull { it.variantId == variantId }?.let { variant ->
                        " (${variant.variantName})"
                    }
                } ?: ""

                Timber.i("adding ${product.name}$variantName to bill")
                val grandTotalWatcher =
                    if (item.minOrder > 0) GrandTotalWatcher(LOWER_THAN, item.minOrder) else null
                freeItemBill.add(
                    Order(
                        product.copy(name = "${product.name}$variantName"),
                        item.qtySelected,
                        info = "free item",
                        promotion = promotionSales,
                        grandTotalWatcher = grandTotalWatcher
                    )
                )
            } ?: run {
                Timber.i("product not found for free ${item.productDetailFkid}")
                _toastMessage.postValue(
                    Event(
                        ToastMessage(
                            "Free item product not found!",
                            Level.ERROR
                        )
                    )
                )
            }
        }

        val effectedBill = arrayListOf<Long>()
        freeItemBill.forEach { freeItem ->
            orders.firstOrNull {
                it.promotion?.promotionId == freeItem.promotion?.promotionId
                        && it.product?.productDetailId == freeItem.product?.productDetailId
            }?.let { order -> //if the same item already in bill, then only update qty
                if (order.isHold && freeItem.qty < order.holdQty) {
                    val newQty = order.holdQty - freeItem.qty
                    val newOrder =
                        order.copy(promotion = null, info = null, qty = newQty, holdQty = newQty)
                    updatePrice(newOrder)
                    orders.add(newOrder)
                    order.holdQty = freeItem.qty
                }
                order.qty = freeItem.qty
                effectedBill.add(order.tmpId)
            } ?: run {
                orders.add(freeItem)
                effectedBill.add(freeItem.tmpId)
            }
        }

        //remove unecessary free item
        freeItems.forEach { freeItem ->
            orders.firstOrNull { it.promotion?.promotionId == freeItem.promotionId && !effectedBill.any { e -> e == it.tmpId } }
                ?.let { order ->
                    orders.remove(order)
                }
        }

        taskRefreshBill.call()
    }

    private fun isMemberEligiblePromotion(promo: PromotionEntity): Boolean {
        if (promo.maximumRedeemPeriod == 0) promo.maximumRedeemPeriod = 1
        val usage = promotionUsage.firstOrNull { it.promotionId == promo.promotionId }?.usage.safe()
        val isMemberEligible =
            promo.memberType?.any { it.typeId == memberDetail?.typeId }
                .safe() && usage < promo.maximumRedeemPeriod.safe(1)
        Timber.d("promoId: ${promo.promotionId} usage: $usage | max Redeem: ${promo.maximumRedeemPeriod} | valid: $isMemberEligible")
        return isMemberEligible
    }

    //voucher free item
    fun addFreeItem(freeItems: List<PromotionFree>) {
        removeFreeItemFromBill()

        promoApplied.firstOrNull()?.let { promo ->
            var parentIds = ""
            promo.promotionDetail?.products?.forEach { product ->
                orders.filter { it.product?.productDetailId == product.productDetailFkid }
                    .forEach { order ->
                        parentIds += "${order.tmpId},"
                    }
            }
            freeItems.filter { it.qtySelected > 0 }.forEach { item ->
                products.firstOrNull { it.productDetailId == item.productDetailFkid }
                    ?.let { product ->
                        val promotionSales = PromotionSales(
                            promotionCode = promo.code,
                            parentId = parentIds,
                            pomotionTypeFkid = promo.typeId
                        )
                        orders.add(
                            Order(
                                product,
                                item.qtySelected,
                                info = "free item",
                                promotion = promotionSales,
                                price = 0,

                                )
                        )
                        Timber.d("[promo] free item added to bill: ${product.name}")
                    } ?: run {
                    Timber.i("product not found for free ${item.productDetailFkid}")
                    _toastMessage.postValue(
                        Event(
                            ToastMessage(
                                "Free item product not found!",
                                Level.ERROR
                            )
                        )
                    )
                }
            }
        }

        promotionList.filter { it.isInRange && it.promotionType?.lowercase() == Constant.PROMO_FREE }
            .forEach { promo ->
                promo.promotionProduct.forEach { promoProduct ->
                    products.firstOrNull { it.productDetailId == promoProduct.productDetailId }
                        ?.let { _ ->
                            if (promoProduct.type == Constant.PROMO_PRODUCT_ORDER) {

                            }
                        }
                }
            }

        taskRefreshBill.call()
    }

    fun addPromoItem(promoItem: List<PromotionFree>) {
        val promo = promoApplied.firstOrNull() ?: return
        Timber.d("[promo] promoItem: ${Gson().toJson(promoItem)}")

        if (promo.type == "special_price" && promo.typeId == Constant.PROMO_TYPE_DEALS) {
            promoItem.filter { it.qtySelected > 0 }.forEach { promoItemSelected ->
                val qtyAdded =
                    orders.filter { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == promoItemSelected.productDetailFkid }
                        .sumOf { it.qty }
                var qtyChange = promoItemSelected.qtySelected - qtyAdded
                Timber.d("[promo] qtyChange: $qtyChange")
                if (qtyChange > 0) {
                    orders.firstOrNull { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == promoItemSelected.productDetailFkid }
                        ?.let { order ->
                            order.qty = order.qty + qtyChange
                        } ?: run {
                        val product =
                            products.firstOrNull { it.productDetailId == promoItemSelected.productDetailFkid }
                        val promoValue =
                            (product?.priceSell.safe() - promoItemSelected.price) * promoItemSelected.qtySelected
                        val promotionSales = PromotionSales(
                            promotionCode = promo.code,
                            pomotionTypeFkid = promo.typeId,
                            promotionValue = promoValue,
                            parentId = getParentIdsOfPromo(promo.toEntity())
                        )
                        orders.add(
                            Order(
                                product,
                                promoItemSelected.qtySelected,
                                info = "(promo -${promoValue.toCurrency()})",
                                promotion = promotionSales,
                                price = promoItemSelected.price,
                                subTotal = promoItemSelected.price * promoItemSelected.qtySelected,
                                extra = promoItemSelected.extra
                                )
                        )
                    }
                } else if (qtyChange < 0) {
                    qtyChange = abs(qtyChange)
                    val orders =
                        orders.filter { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == promoItemSelected.productDetailFkid }
                    for (order in orders) {
                        break
                    }

                    orders.filter { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == promoItemSelected.productDetailFkid }
                        .forEach { order ->
                            if ((order.qty - qtyChange) >= 0) {
                                order.qty -= qtyChange
                                qtyChange -= 0
                            } else {
                                order.qty = 0
                                qtyChange -= order.qty
                            }

                            if (qtyChange <= 0) {
                                return@forEach
                            }
                        }
                }
            }
        }
        reCalculateTax()
        taskRefreshBill.call()
        taskRefreshTax.call()
    }

    //list to show to user any free item product available
    fun createFreeItemList(filterPromoId: Int = 0): List<PromotionFree> {
        val promotionFreeList = ArrayList<PromotionFree>()
        val promotionFreeListUnused = ArrayList<PromotionFree>()
        val grandTotal = getGrandTotalRealPrice()
        var maxFree: Int
        val promoFreeTypes =
            listOf(Constant.PROMO_FREE, Constant.PROMO_FREE_MEMBER)//, Constant.PROMO_FREE_VOUCHER

        Timber.i(">> Filter [promo] (vm): $filterPromoId")
        fun isIdMatch(promo: PromotionEntity): Boolean {
            if (filterPromoId > 0) {
                return promo.promotionId == filterPromoId
            }
            return true
        }

        Timber.d("[promo] ${Gson().toJson(promotionList.filter { isIdMatch(it) })}")
        promotionList.filter { p ->
            p.isInRange && isIdMatch(p) && promoFreeTypes.any {
                it.equalIgnoreCase(
                    p.promotionType
                )
            }
        }
            .forEach { promo ->
                var termFound = 0
                val productBuy = StringBuilder()
                val productBuyIds = ArrayList<Int>()
                val productFree = ArrayList<PromotionFree>()
                var qtyBuy = 0
                var qtyFree = 0

                Timber.d("[promo] promo free '${promo.name}' : ${Gson().toJson(promo.promotionProduct)}")
                promo.promotionProduct.distinctBy { Pair(it.productDetailId, it.type) }
                    .forEach { promoProduct ->
                        Timber.d("[promo] promo product -> ${Gson().toJson(promoProduct)}")
                        products.firstOrNull { it.productDetailId == promoProduct.productDetailId }
                            ?.let { product ->
                                if (promoProduct.type == Constant.PROMO_PRODUCT_ORDER) {
                                    val variantName = product.variantFkid?.let { variantId ->
                                        variants.firstOrNull { it.variantId == variantId }
                                            ?.let { "(${it.variantName})" }
                                    } ?: ""

                                    productBuy.appendnl("- ${product.name} $variantName")
                                    productBuyIds.add(product.productDetailId)
                                    qtyBuy = promoProduct.qty

                                    orders.filter { it.product?.productDetailId == promoProduct.productDetailId && it.promotion?.pomotionTypeFkid != Constant.PROMO_TYPE_FREE }
                                        .forEach { order ->
                                            if (order.isItemVoid && order.qty > 0) {
                                                termFound -= order.qty
                                            } else {
                                                termFound += order.qty
                                            }
                                            Timber.i("term found -- qty : ${order.qty} (${order.product?.name}) --> $termFound")
                                        }
                                } else {
                                    Timber.d("[promo] adding to free '${product.name}' varian : ${product.variantFkid}")
                                    val variantName =
                                        variants.firstOrNull { it.variantId == product.variantFkid }
                                            ?.let { " (${it.variantName})" }
                                            ?: ""
                                    qtyFree = promoProduct.qty
                                    val qty =
                                        orders.filter { it.promotion?.promotionId == promo.promotionId && it.product?.productDetailId == product.productDetailId }
                                            .sumOf { it.qty }
                                    productFree.add(
                                        PromotionFree(
                                            (product.name
                                                ?: "-") + variantName,
                                            product.priceSell
                                                ?: 0,
                                            promotionId = promo.promotionId,
                                            productDetailFkid = product.productDetailId,
                                            qtySelected = qty,
                                            promoName = promo.name,
                                            promotionTypeId = promo.promotionTypeId.safe(),
                                            minOrder = promo.minOrder
                                        )
                                    )
                                }
                            } ?: kotlin.run {
                            Timber.i("[promo] product with id '${promoProduct.productDetailId}' not found!")
                        }
                    }

                if (productFree.isNotEmpty()) {
                    Timber.i("'${promo.name}' (${promo.promotionType}) -- termFound $termFound | minBuy : $qtyBuy")
                    var specialInfo = ""
                    var additionalInfo = promo.takeIf { it.minOrder > 0 }
                        ?.let { "MINIMUM ORDER Rp${it.minOrder.toCurrency()}\n" }.safe()
                    if (promo.promotionType.equalIgnoreCase(Constant.PROMO_FREE_MEMBER)) {
                        if (promo.memberType?.any { it.typeId == memberDetail?.typeId } == false) {
                            Timber.i("termFound reset to zero because current member is ${memberDetail?.typeId}")
                            termFound = 0
                        }
                        additionalInfo += "KHUSUS MEMBER : ${
                            promo.memberType?.map { it.name }?.joinToString(", ")
                        }\n"
                    } else if (promo.promotionType.equalIgnoreCase(Constant.PROMO_FREE_VOUCHER)) {
                        //it its promo free voucher, check if voucher applied
                        val isVoucherApplied =
                            promoApplied.any { it.promotionId == promo.promotionId }
                        if (!isVoucherApplied) termFound = 0
                        specialInfo = "(harus input Voucher) \n"
                    }

                    var promoTerm =
                        "Item yang harus dibeli (min $qtyBuy) : \n$productBuy\n$additionalInfo"
                    if (qtyBuy == 0) {
                        qtyBuy = 1
                    }

                    maxFree =
                        if (grandTotal >= promo.minOrder) (termFound / qtyBuy) * qtyFree else 0

                    Timber.d("[promo] applied: ${Gson().toJson(promoApplied)}")

                    //this will put them on top
                    if (maxFree > 0) {
                        promotionFreeList.add(
                            PromotionFree(
                                promoName = promo.name,
                                promoInfoDetail = promoTerm,
                                promoQtyInfo = "${specialInfo}Maximum Free Item : $maxFree",
                                qtyMax = maxFree,
                                isHeader = true,
                                productBuyIds = productBuyIds
                            )
                        )
                        promotionFreeList.addAll(productFree)
                    } else {
                        promotionFreeListUnused.add(
                            PromotionFree(
                                promoName = promo.name,
                                promoInfoDetail = promoTerm,
                                promoQtyInfo = "Maximum Free Item : $maxFree",
                                qtyMax = maxFree,
                                isHeader = true,
                                productBuyIds = productBuyIds
                            )
                        )
                        promotionFreeListUnused.addAll(productFree)
                    }
                } else {
                    Timber.i("Product Free is empty for promo : ${promo.name}")
                }
            }

        promotionFreeList.addAll(promotionFreeListUnused)
        Timber.i("[promo] promotionFreeList ${promotionFreeList.size}")
        return promotionFreeList
    }

    fun createPromotionFreeList(): List<PromotionFree> {
        val promotionFreeList = ArrayList<PromotionFree>()
        var maxFree: Int
        promoApplied.firstOrNull { it.type == "free" }?.let { promo ->
            Timber.i("${promo.code} - ${promo.displayVoucher}")
            var termFound = 0
            var promoTerm =
                "Item yang harus dibeli (min ${promo.promotionDetail?.products?.firstOrNull()?.qty}) : \n"
            promo.promotionDetail?.products?.forEach { productBuy ->
                products.firstOrNull { it.productDetailId == productBuy.productDetailFkid }?.let {
                    val variantName = it.variantFkid?.let { variantId ->
                        variants.firstOrNull { v -> v.variantId == variantId }
                            ?.let { variant -> "(${variant.variantName})" }
                    }
                        ?: ""
                    promoTerm += "- ${it.name} $variantName\n"
                }
                orders.filter { it.product?.productDetailId == productBuy.productDetailFkid }
                    .forEach { order ->
                        if (order.isItemVoid && order.qty > 0) {
                            termFound -= order.qty
                        } else {
                            termFound += order.qty
                        }
                        Timber.i("term found - qty : ${order.qty} (${order.product?.name})")
                    }
            }

            Timber.i("qty should buy: ${promo.promotionDetail?.products?.firstOrNull()?.qty} qty Free:${promo.promotionDetail?.productsFree?.firstOrNull()?.qty}")
            maxFree = (termFound / promo.promotionDetail?.products?.firstOrNull()?.qty.safe(1)) * promo.promotionDetail?.productsFree?.firstOrNull()?.qty.safe(1)
            promotionFreeList.add(
                PromotionFree(
                    promoName = promo.name,
                    qtyMax = maxFree,
                    promoQtyInfo = "Maximum Free Item : $maxFree",
                    promoInfoDetail = promoTerm,
                    isHeader = true
                )
            )
            promo.promotionDetail?.productsFree?.forEach { productFree ->
                products.firstOrNull { it.productDetailId == productFree.productDetailFkid }
                    ?.let { product ->
                        val qty =
                            orders.filter { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == productFree.productDetailFkid }
                                .sumOf { it.qty }
                        promotionFreeList.add(
                            PromotionFree(
                                productName = product.getName(variants),
                                price = product.priceSell.safe(),
                                productDetailFkid = productFree.productDetailFkid,
                                qtySelected = qty
                            )
                        )
                    }
            }
        }
        return promotionFreeList
    }

    fun createPromotionItemList(): List<PromotionFree> {
        val promotionItemList = ArrayList<PromotionFree>()
        val promo = promoApplied.firstOrNull() ?: return promotionItemList
        Timber.d("[promo] promo: ${Gson().toJson(promo)}")

        if (promo.type == "special_price" && promo.typeId == Constant.PROMO_TYPE_DEALS) {
            val terms = ArrayList<com.uniq.uniqpos.data.local.entity.PromotionProduct>()
            val productTerms =
                promo.toEntity().promotionProduct.filter { it.type == Constant.PROMO_PRODUCT_ORDER }
            terms.addAll(productTerms)

            var promoInfo = StringBuilder(promo.term.removeHtmlTag())
            if (productTerms.isNotEmpty()) {
                promoInfo.appendLine("\nSyarat item yang harus di beli (min ${productTerms[0].qty}) : ")
            }
            productTerms.forEach { product ->
                products.firstOrNull { it.productDetailId == product.productDetailId }?.let {
                    promoInfo.appendLine(" - ${it.name}")
                }
            }

            var qtyMax = promo.maxQtyPromo.safe()
//            promo.termsMultipleApply?.takeIf { promo.maxQtyPromo.safe() > 0 && it > 0 }?.let {
//                qty = (qtyTermAdded/productTerm.first().qty) * promo.maxQtyPromo.safe()
//                Timber.d("[promo] (termsMultipleApply) qtyTermAdded: $qtyTermAdded, terms: ${productTerm.first().qty}, maxQty: ${promo.maxQtyPromo}")
//            }

            promotionItemList.add(
                PromotionFree(
                    promoName = promo.name,
                    promoQtyInfo = if (promo.maxQtyPromo == null) "Promo Item Has No Limit" else "Maximum Item : ${promo.maxQtyPromo}",
                    isHeader = true,
                    qtyMax = promo.maxQtyPromo.safe(),
                    isUnlimitedItem = promo.maxQtyPromo == null,
                    productTerms = terms,
                    promoInfoDetail = promoInfo.toString()
                )
            )

            promo.promotionDetail?.products?.filter { it.type != Constant.PROMO_PRODUCT_ORDER }
                ?.forEach { productPromo ->
                    products.firstOrNull { it.productDetailId == productPromo.productDetailFkid }
                        ?.let { product ->
                            val qty =
                                orders.filter { p -> p.promotion?.promotionCode == promo.code && p.product?.productDetailId == productPromo.productDetailFkid }
                                    .sumOf { it.qty }
                            promotionItemList.add(
                                PromotionFree(
                                    productName = product.getName(variants),
                                    price = productPromo.price,
                                    productDetailFkid = productPromo.productDetailFkid,
                                    qtySelected = qty
                                )
                            )
                        }
                }
        }
        return promotionItemList
    }

    fun runGrandTotalWatcher() {
        viewModelScope.launch {
            var isFound = false
            val grandTotal = getGrandTotalRealPrice()
            Timber.d("[promo] grand : $grandTotal | watcher --> ${Gson().toJson(orders.filter { it.grandTotalWatcher != null })}")

            //to add promo
            orders.filter { it.grandTotalWatcher?.type == GREATER_THAN && it.grandTotalWatcher?.value.safe() <= grandTotal }
                .forEach { order ->
                    updatePrice(order)
                    isFound = true
                    Timber.i("[promo] updating bill $GREATER_THAN with id ${order.tmpId} '${order.product?.name}'")
                }

            //to remove promo
            orders.filter {
                it.grandTotalWatcher?.type == LOWER_THAN &&
                        it.grandTotalWatcher?.value.safe() > grandTotal
            }.forEach { order ->
                //promotion should be removed, func 'updatePrice' will ignore to recalculate price if order has promotion
                Timber.i("[promo] removing order with promo type ${order.promotion?.pomotionTypeFkid}")
                var notifMsg: String? = null
                if (order.promotion?.pomotionTypeFkid == 6 && !order.isHold) {
                    orders.remove(order)
                    notifMsg = "free item telah dihapus dari bill karena tidak memenuhi syarat"
                } else {
                    if (order.promotion?.pomotionTypeFkid == 6) {
                        notifMsg =
                            "harga free item diubah menjadi normal kerana tidak memenuhi syarat"
                    }
                    order.promotion = null
                    order.info = null
                    updatePrice(order)
                }
                notifMsg?.let {
                    _snackbarText.postValue(
                        Event(
                            SnackbarEvent(
                                notifMsg,
                                timeLength = Snackbar.LENGTH_LONG
                            )
                        )
                    )
                }
                isFound = true
                Timber.i("[promo] updating bill $LOWER_THAN with id ${order.tmpId} '${order.product?.name}'")
            }

            if (isFound) {
                refreshOrder.call()
                reCalculateTax()
                taskRefreshTax.call()
            }
        }
    }

    fun updatePromotionValue(order: Order) {
        order.takeIf { it.promotion != null }?.let {
            var total = (order.product?.priceSell.safe() * order.qty) - abs(order.subTotal)
            if (order.isItemVoid) total *= -1
            order.promotion?.promotionValue = total
            Timber.d("[promo] update promotion value, $total : ${order.product?.priceSell.safe()} x ${order.qty} - ${order.subTotal}")
        }
    }

    fun clearZeroTax() {
        for (i in taxSaleList.size - 1 downTo 0) {
            if (taxSaleList[i].total <= 0) taxSaleList.removeAt(i)
        }
    }

    fun updateTableStatus(diningTableEntity: DiningTableEntity) {
        viewModelScope.launch {
            outletRepository.updateTableStatus(diningTableEntity)
        }
    }

    fun resetTransaction(isRemovePromotion: Boolean = false, isRefundPromotion: Boolean = false) {
        orders.clear()
        taxSaleList.clear()
        taxesEnable.clear()

        //when not in onDestroy state, may be when user use Reset Bill function
        //and when sales is not from cart
        //then remove the voucher
        if (isRemovePromotion) {
            if (!isSalesEdit){//if not cart, we need to refund the promo
                promoApplied.firstOrNull()?.let { promo ->
                    val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
                    removePromotion(promo, outlet?.outletId.safe())
                }
            }else{
                promoApplied.clear()
            }
        } 
        initDefaultTax()
        salesEdit = null
        isSalesEdit = false
        choosenEmployee = null
        isMergeBill = false
        memberId = null
        memberDetail = null
        contactSelfOrder = null
        customerName = null
        diningTable = null
        discount = Discount()
        mergeIds.clear()
        taskRefreshPromo.call()
        taskMemberRemoved.call()
//        Timber.i(Throwable("reset transaction").stackTraceToString())
    }

    fun savePendingPrint(pendingPrintEntity: List<PendingPrintEntity>) {
        viewModelScope.launch {
            salesRepository.savePendingPrint(pendingPrintEntity)
        }
    }

    fun findMember(barcode: String, outletId: Int?, isSilent: Boolean = false) {
        viewModelScope.launch {
            try {
                Timber.i("get member: $barcode")
                if (!isSilent) pDialogTask.call<String>()
                val resp = salesRepository.checkMember(barcode, outletId)
                resp.data?.let { setMember(it) }
                if (!isSilent) pDialogTask.call<String>()
                taskMemberFound.postValue(resp)
            } catch (e: Exception) {
                if (!isSilent) taskShowMessage.postValue(e)
            }
        }
    }

    fun findMemberWithSecretCode(secretCode: String) {
        viewModelScope.launch {
            try {
                pDialogTask.postValue("Loading...")
                val resp = salesRepository.checkSecretCode(secretCode)
                resp.data?.let { setMember(it) }
                pDialogTask.call<String>()
                taskMemberFound.postValue(resp)
            } catch (e: Exception) {
                taskShowMessage.postValue(e)
            }
        }
    }

    fun setMember(member: Member) {
        memberId = member.memberId
        memberDetail = member
        if (promotionList.any { it.memberType != null }) {
            var isUsePromoMember = false
            orders.forEach {
                updatePrice(it)

                //if subtotal has changed, may be promo member is applied
//                val subTotal = it.subTotal
//                if (it.subTotal != subTotal) {
//                    isUsePromoMember = true
//                }
            }
            reCalculateTax()
            taskRefreshTax.call()
            taskRefreshBill.call()
            if (isUsePromoMember) {
                _toastMsg.value = Event("promo Member berhasil digunakan")
            }
        }

        Timber.i("set member: ${member.memberId}")
//        vmPromo.callMe()
        getMemberPromoUsage(memberId = memberId.safeToInt())
    }

    fun removeMember() {
        memberId = null
        memberDetail = null
        salesEdit?.let { edit ->
            edit.memberId = null
            edit.memberDetail = null
            edit.customer = null
        }
        taskMemberRemoved.call()
    }

    fun checkPromotionCode(barcode: String, outletId: Int?) {
        Bugsnag.leaveBreadcrumb("checking voucher code $barcode")
        viewModelScope.launch {
            var promoCode: String? = ""
            try {
                val grandTotal = calculateGrandTotal(false)
                val resp = salesRepository.checkPromotionCode(barcode, outletId)
                resp.takeIf { it.status }?.data?.let { promotion ->
                    promoCode = promotion.code
                    Timber.i("grandTotal : $grandTotal | minOrder : ${promotion.minOrder}")
                    if (promotion.minOrder <= grandTotal) {
                        promotion.displayVoucher = barcode
                        if (promotion.source == Constant.PROMO_SOURCE_UNIQ) {
                            promotion.displayVoucher = barcode.takeMax(13, "").uppercase()
                        }
                        promoApplied.clear()
                        promoApplied.add(promotion)
                        promotion.memberDetail?.let { member ->
                            setMember(member)
                            taskMemberFound.postValue(ServerResponse(status = true, data = member))
                        }
                        Timber.i(
                            "[promo] Voucher $barcode applied. detail : ${
                                Gson().toJson(
                                    promotion
                                )
                            }"
                        )

                        promotion.promotionDetail?.products?.let { promoProductList ->
                            promoProductList.forEach { promoProduct ->
                                orders.firstOrNull { it.product?.productDetailId == promoProduct.productDetailFkid }
                                    ?.let { order ->
                                        updatePrice(order)
                                    }
                            }
                            taskRefreshBill.call()
                        }

                        reCalculateTax()
                        refreshPromo(isAppliedManual = true)
                        taskPromotionResult.postValue(null)//dismiss voucher input dialog, set null for error
                        updatePromotionToDb(promo = promotion)
//                        promotion.typeId == Constant.PROMO_TYPE_FREE

                        taskShowVoucherDetail.call()
                    } else {
                        taskPromotionResult.postValue(Exception("min order is ${promotion.minOrder.toCurrency()}"))
                        removePromotion(promotion, outletId ?: 0)
                    }
                } ?: run {
                    taskPromotionResult.postValue(
                        Exception(
                            resp.message
                                ?: "Voucher not found or expired!"
                        )
                    )
                    resp.data?.usage?.takeIf { it.isNotEmpty() }?.let { promotionUsages ->
                        taskPromotionUsageDialog.postValue(promotionUsages)
                    }
                }
            } catch (e: Exception) {
                Timber.i("checking promotion error - $e")
                taskPromotionResult.postValue(e)
                if (promoCode.isNullOrBlank()) {
                    removePromotionByCode(barcode, outletId.safe())
                } else {
                    removePromotion(Promotion(promoCode), outletId ?: 0)
                }
            }
        }
    }

    private fun updatePromotionToDb(isAdd: Boolean = true, promo: Promotion? = null) {
        if (isSalesEdit) {
            val outlet = sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)
            val sales = getSalesEntity().copy()
            val tmpSales = TmpSalesEntity(sales.noNota, Gson().toJson(sales), outlet?.outletId!!)
            updateTmpSale(tmpSales)
        } else {
            if (isAdd) {
                promo?.let { sharedPref.saveJson(SharedPref.TEMP_VOUCHER, promo) }
            } else {
                sharedPref.clearJson(SharedPref.TEMP_VOUCHER)
            }
        }
    }

    //restore promotion from shared pref if any
    fun restorePromotion() {
        sharedPref.getJson(SharedPref.TEMP_VOUCHER, Promotion::class.java)?.let { promotion ->
            promoApplied.clear()
            promoApplied.add(promotion)
            taskRefreshPromo.call()
            reCalculateTax()
            taskRefreshTax.call()
        }
    }

    fun removeBehavePromotion(promotion: Promotion, outletId: Int?) {
        viewModelScope.launch {
            try {
                pDialogTask.postValue("Loading...")
                salesRepository.voidPromotion(promotion.code, outletId)
                taskVoidVoucher.postValue(promotion)
                pDialogTask.call<String>()
            } catch (e: Exception) {
                taskShowMessage.postValue(e)
            }
        }
    }

    fun removePromotion(promo: Promotion, outletId: Int) {
        Timber.i("remove [promo] : ${promo.code} | source : ${promo.source}")
        viewModelScope.launch {
            try {
                pDialogTask.postValue("Loading...")
                salesRepository.voidPromotion(promo.code, outletId)

                promoApplied.clear()
                Timber.d("type : ${promo.type}")
                if (promo.type == "free") {
                    removeFreeItemFromBill()
                } else {
                    var count = 0
                    orders.filter { it.promotion != null }.forEach { order ->
                        order.promotion = null
                        order.info = null
                        updatePrice(order)
                        count++
                    }
                    Timber.d("updated orders : $count")
                }

                reCalculateTax()
                taskRefreshPromo.call()
                taskRefreshBill.call()
                updatePromotionToDb(false)
            } catch (e: Exception) {
                taskShowMessage.postValue(e)
            } finally {
                pDialogTask.call<String>()
            }
        }
    }

    //code: gained from scan qrcode
    //should only call if getting promo by code failed (app didn't receive any promotion yet)
    private fun removePromotionByCode(code: String, outletId: Int) {
        Timber.i("remove promotion by code : $code")
        viewModelScope.launch {
            //add to pending remove
            var pendingList = ArrayList<String>()

            try {
                val type = object : TypeToken<ArrayList<String>>() {}.type
                val dataJson = sharedPref.getString(SharedPref.PENDING_VOUCHER_REMOVE, "")
                pendingList = Gson().fromJson<ArrayList<String>>(dataJson, type)
                if (!pendingList.any { it == code }) {
                    pendingList.add(code)
                }
                sharedPref.saveJson(SharedPref.PENDING_VOUCHER_REMOVE, pendingList)
            } catch (e: Exception) {
                Timber.i("save or get pending voucher err: $e")
            }

            try {
                salesRepository.voidPromotion(code, outletId)
                pendingList.remove(code)
                sharedPref.saveJson(SharedPref.PENDING_VOUCHER_REMOVE, pendingList)
                Timber.i("promo code: $code removed")
            } catch (e: Exception) {
                Timber.i("void promo code: '$code' error: $e")
            }
        }
    }

    private fun removeFreeItemFromBill() {
        Timber.d("[promo] orders: ${Gson().toJson(orders)}")
        orders.filter { it.promotion?.promotionCode != null && it.promotion?.parentId != null }
            .forEach { order ->
                if (order.isHold) {
                    order.price = order.product?.priceSell ?: 0
                    order.qty = order.holdQty
                    order.promotion = null
                    order.info = null
                } else {
                    orders.remove(order)
                }
            }
    }

    fun removeMemberPromo() {
        // it.promotion?.pomotionTypeFkid == Constant.PROMO_TYPE_FREE_MEMBER
        orders.filter { order -> promoMemberTypes.any { order.promotion?.pomotionTypeFkid == it } }
            .forEach { order ->
                Timber.d("${order.product?.name} use promo: ${order.promotion?.pomotionTypeFkid}")
//            if (order.isHold || order.promotion?.pomotionTypeFkid == Constant.PROMO_TYPE_SPECIAL_PRICE_MEMBER) {
                Timber.i("price sell -> ${order.product?.name} Rp${order.product?.priceSell}")
                order.price = order.product?.priceSell ?: 0
//            if (order.isHold) order.qty = order.holdQty
                order.promotion = null
                order.info = null
                updatePrice(order)
//            } else {
//                orders.remove(order)
//            }
            }
        promoApplied.firstOrNull { it.typeId == Constant.PROMO_TYPE_DEALS }?.let {
            val outletId =
                sharedPref.getJson(SharedPref.OUTLET_DATA, Outlet::class.java)?.outletId.safe()
            removePromotion(it, outletId)
        }
        reCalculateTax()
        taskRefreshBill.call()
        taskRefreshTax.call()
    }

    fun syncSales(data: SalesEntity) {
        Timber.i("Sync sales. SalesId : ${data.noNota} | DisplayNota : '${data.displayNota}'")
        viewModelScope.launch {
            salesRepository.directSyncSales(data)
        }
    }

    fun updateCartToPaid(ids: List<String>?) {
        viewModelScope.launch {
            Timber.i("sync cart. ids : $ids")
            ids?.forEach { id -> salesRepository.updateCartToPaid(id) }
        }
    }

    fun updateProduct(productEntity: ProductEntity?) {
        viewModelScope.launch {
            productEntity?.let { product ->
                productRepository.updateProduct(product, true)
                refreshProduct.call()
            }
        }
    }

    fun updateProductPhoto(productId: Int, photoFile: File) {
        viewModelScope.launch {
            try {
                _loadingDialog.postValue(Event(true))
                Timber.i("update photo product : $productId")
                val result = productRepository.updateProductPhoto(productId, photoFile)
                _toastMessage.postValue(Event(ToastMessage("update photo success", type = Level.INFO)))
                taskPhotoUpdated.postValue(result.safe())
            } catch (e: Exception) {
                Timber.e(e)
                _toastMessage.postValue(Event(ToastMessage(e.message ?: "failed", type = Level.ERROR)))
                //_error.postValue(e.message)
            }finally {
                _loadingDialog.postValue(Event(false))
            }
        }
    }

    fun isParentFromPromo(currentOrder: Order): Boolean {
        val isParentOfTaxPromo =
            taxSaleList.filter { it.identifierType == Constant.TAX_TYPE_PROMOTION }
                .any { it.parentIds.safe().contains(currentOrder.tmpId.toString()) }
        val isParentOfPromo =
            orders.any { it.promotion?.parentId?.contains(currentOrder.tmpId.toString()) == true }
        Timber.d("[promo] isParentOfTaxPromo: $isParentOfTaxPromo | isParentOfPromo: $isParentOfPromo | ${currentOrder.product?.name} - ${currentOrder.tmpId}")
        Timber.d("[promo] parents: ${orders.map { "${it.promotion?.parentId}(${it.product?.name})|" }}")
        return isParentOfPromo || isParentOfTaxPromo
    }

    fun isUseMemberPromo(): Boolean {
        return orders.any { order -> promoMemberTypes.any { order.promotion?.pomotionTypeFkid == it } } ||
                promoApplied.any { it.typeId == Constant.PROMO_TYPE_DEALS }
    }

    fun checkBarcodeProduct(barcode: String) {
        viewModelScope.launch {
            var barcodeType = "[NONE]"
            products.firstOrNull { it.barcode == barcode }?.let { product ->
                barcodeType = "product"
                barcodeResult.postValue(Pair(product, false))
            } ?: run {
                //look for varian
                variants.firstOrNull { it.variantBarcode == barcode }?.let { variant ->
                    barcodeType = "variant"
                    productRepository.getProductByVariantIdSuspend(variant.variantId)
                        ?.let { product ->
                            barcodeResult.postValue(
                                Pair(
                                    product.copy(name = "${product.name} (${variant.variantName})"),
                                    true
                                )
                            )
                        } ?: run {
                        dialogMessageTask.postValue("barcode recognized as product variant, but we could find the product!")
                    }
                }
            }
            Timber.i("Barcode '$barcode' is $barcodeType")
        }
    }

    enum class CategoryType {
        MAIN, EXTRA
    }

    fun initSalesData(
        sales: SalesEntity,
        mergedIds: ArrayList<String>? = null,
        isFromCart: Boolean = true
    ) {
        resetTransaction()

        orders.addAll(sales.orderList as ArrayList)
        if (isFromCart) {
            orders.forEach { order ->
                order.isHold = true
                order.holdQty = order.qty

                //update product based on recent data
                products.firstOrNull { it.productDetailId == order.product?.productDetailId }
                    ?.let { product ->
                        order.product?.discount = product.discount
                    }
            }
        }

        taxSaleList.clear()
        sales.taxes?.let { taxSaleList.addAll(it) }
        sales.discount?.let {
            discount = it
        }

        Timber.i(">>> Gratuity from saved list : ${Gson().toJson(taxSaleList)}")
        taxesEnable.clear()
        taxSaleList.forEach {
            taxesEnable.put(it.id ?: 0, true)
            Timber.i(">>> ${it.name} (${it.id}) : ${it.total} ")
        }

        promoApplied.clear()
        sales.promotions?.filter { it.appliedByManual }?.let {
            promoApplied.addAll(it)
        }

        isSalesEdit = true
        salesEdit = sales
        memberId = sales.memberId
        Timber.i("member detail : ${sales.memberDetail}")

        sales.memberDetail?.takeIf { it.memberId.safeToInt() > 0 }?.let {
            memberDetail = it
            taskMemberFound.postValue(ServerResponse(status = true, data = it))
        }

        mergedIds?.let { ids ->
            isMergeBill = true
            mergeIds.addAll(ids)
            Timber.i("merge ids : $ids")

            //if there any promotion active, try to recalculate price (in case promo apply)
            if (promotionList.isNotEmpty()) {
                orders.forEach { order -> updatePrice(order) }
            }
        }

        reCalculateTax()
        refreshOrder.call() //must execute at the last (after all set)
        if (isFromCart) {
            taskReceiveCart.call() //must execute at the last (after all set)
        }
    }

    fun findSelfOrder(code: String, outletId: Int?) {
        viewModelScope.launch {
            pDialogTask.postValue("Loading...")
            try {
                val resp = salesRepository.getSelfOrder(code).await()
                if (resp.status) {
                    if (resp.data?.outletId == outletId) {
                        val productsOrder = parseToOrderList(resp.data?.orderList, products, variants)

                        if (productsOrder.isNotEmpty()) resetTransaction(isRemovePromotion = false)
                        productsOrder.forEach { order ->
                            updatePrice(order)
                            orders.add(order)
                        }
                        reCalculateTax()
                        refreshOrder.call()

                        taskFoundSelfOrder.postValue(productsOrder)
                        contactSelfOrder = resp.data?.contact
                        customerName = resp.data?.customerName
                        diningTable = resp.data?.diningTable
                        resp.data?.member?.let { memberDetail = it }
                    } else {
                        dialogMessageTask.postValue("order tidak dapat dilakukan di outlet ini")
                    }
                } else {
                    dialogMessageTask.postValue(resp.message ?: "Gagal mendapatkan data dari server")
                }

            } catch (e: Exception) {
                Timber.i("getting self order failed - $e")
                dialogMessageTask.postValue(e.readableError())
            }
            pDialogTask.call<String>()
        }
    }

    fun updatePromotionList(data: List<PromotionEntity>) {
        var log = StringBuilder("[promo] all promotion size : (${data.size}) \n")
//        data.forEach { promo -> log.appendLine("${promo.copy(promotionProduct = arrayListOf())} -> applied for ${promo.promotionProduct.size} products") }

        promotionList.clear()
        //since, deals is by voucher code/scan only, so we do not need it
        promotionList.addAll(data.filter { it.promotionType != Constant.PROMO_DEALS })

        log.append("promoList: ")
        promotionList.forEach {
            log.append("${it.promotionId} '${it.name}' isInRange : ${it.isInRange}, ")
        }

        log.appendnl("--> total promo active : ${promotionList.size}")
//        log.appendnl("--> ids: ${promotionList.joinToString { it.promotionId.toString() }}")
        Timber.i("total product promo: [1] vm ${promotionList.firstOrNull()?.promotionProduct?.size}")
        Timber.i(log.toString())
        taskPromotionListRefresh.call()
    }

    fun syncOrderSales(outletId: Int?) {
        viewModelScope.launch {
            withContext(Dispatchers.IO) {
                salesRepository.syncOrderSales(outletId ?: 0)
            }
        }
    }

    fun isValidSplitBill(originOrderList: java.util.ArrayList<Order>?): Exception? {
        //check item in origin order
        originOrderList?.filter { it.isItemVoid }?.groupBy { it.voidParentId }?.let { orderGroup ->
            for ((parentId, order) in orderGroup) {
                val voidQty = order.sumOf { it.qty }
                val originQty = originOrderList.filter { it.tmpId == parentId }.sumOf { it.qty }
                Timber.d("voidQty : $voidQty - originQty : $originQty")
                if (voidQty > originQty) {
                    return Exception("Qty yang dipilih tidak sesuai! \nterdapat void untuk item ${order.first().product?.name}")
                }
            }
        }

        //check split item
        orders.filter { it.isItemVoid }.groupBy { it.voidParentId }.let { orderGroup ->
            for ((parentId, order) in orderGroup) {
                val voidQty = order.sumOf { it.qty }
                val originQty = orders.filter { it.tmpId == parentId }.sumOf { it.qty }
                if (voidQty > originQty) {
                    return Exception("Qty void yang dipilih tidak sesuai! \nterdapat void untuk item ${order.first().product?.name}")
                }
            }
        }
        return null
    }

    //return true if current order has void item
    fun updateDiscountVoid(originOrder: Order): Boolean {
        var isFound = false
        orders.filter { it.isItemVoid && it.voidParentId == originOrder.tmpId }
            .forEach { orderVoid ->
                //if discount is nominal, remove it
                if (originOrder.discount.discountType == Constant.TYPE_NOMINAL) {
                    orderVoid.discount = Discount()
                } else {
                    orderVoid.discount = originOrder.discount
                }
                isFound = true
            }
        return isFound
    }

    fun removeBill(order: Order) {
        order.qty = 0
        updatePrice(order)
        order.extra.forEach { it.qty = 0 }
        reCalculateTax()
        Timber.d("removing.... ${order.product?.name} (${order.product?.productDetailId}) with total extra : ${order.extra.size}")
        orders.remove(order)
    }

    fun saveToCart(sales: SalesEntity, outletId: Int, isDirectSync: Boolean = true) {
        viewModelScope.launch {
            val salesJson = Gson().toJson(sales)
            Timber.i("saving cart : $salesJson")

            sales.orderList?.forEach { it.printed = it.qty }
            var tmpSales = TmpSalesEntity(sales.noNota, Gson().toJson(sales), outletId)

            salesRepository.saveTmpSales(tmpSales, isDirectSync)
            salesEdit?.let {
//                salesRepository.updateTmpSale(tmpSales, isDirectSync)
            } ?: run {
//                salesRepository.saveTmpSales(tmpSales, isDirectSync)

                //if there is a promotion, remove from tmp db (if necessary)
//                sales.promotions?.firstOrNull()?.let { promoApplied ->
//                    sharedPref.getJson(SharedPref.TEMP_VOUCHER, Promotion::class.java)?.let { promotion ->
//                        if (promotion.code == promoApplied.code) {
//                            sharedPref.clearJson(SharedPref.TEMP_VOUCHER)
//                        }
//                    }
//                }
            }

            //if there is a promotion, remove from tmp db (if necessary)
            sales.promotions?.firstOrNull()?.let { promoApplied ->
                sharedPref.getJson(SharedPref.TEMP_VOUCHER, Promotion::class.java)
                    ?.let { promotion ->
                        if (promotion.code == promoApplied.code) {
                            sharedPref.clearJson(SharedPref.TEMP_VOUCHER)
                        }
                    }
            }

            if (sales.table.isNotEmpty()) {
                outletRepository.updateTableStatus(
                    DiningTableEntity(
                        tableName = sales.table,
                        status = Constant.TABLE_STATUS_USED
                    )
                )
            }

            mergeIds.forEach { id ->
                salesRepository.updateTmpSale(
                    TmpSalesEntity(
                        id,
                        status = Constant.SALES_STATUS_PAID,
                        outletId = outletId
                    ), isDirectSync
                )
                Timber.i("Remove Merge Ids : $id")
            }

            resetTransaction()
            taskCartSaved.postValue(Gson().fromJson(salesJson, SalesEntity::class.java))
            restorePromotion()
        }
    }

    fun sendCartToLocalServer(salesCartId: String) {
        viewModelScope.launch {
            salesRepository.getSalesCartById(salesCartId).let { cart ->
                taskSendToLocalServer.postValue(cart)
            }
        }
    }

    fun isNeedToRecalculateTax(): Boolean {
        var isNeedToRecalculateTax = false
        promoApplied.firstOrNull { it.minOrder > 0 }?.let { promo ->
            isNeedToRecalculateTax = calculateGrandTotal(false) >= promo.minOrder
        }

        if (!isNeedToRecalculateTax && promotionList.isNotEmpty()) {
            isNeedToRecalculateTax =
                promotionList.any { it.discountType == Constant.PROMO_DISC_TYPE_NOTA }
        }

        if (!isNeedToRecalculateTax && promoApplied.isNotEmpty()) {
            isNeedToRecalculateTax =
                !taxSaleList.any { it.identifierType == Constant.TAX_TYPE_PROMOTION && it.identifierId == promoApplied.first().code }
        }
        return isNeedToRecalculateTax
    }

    fun getNoteHistoryByProduct(id: Int): ArrayList<String> {
        var list = notesHistory.filter { it.productId == id }.take(20).flatMap { it.notes }

        //if getting by product returns empty, use from all products
        if (list.isEmpty()) list = notesHistory.take(20).flatMap { it.notes }

        //make sure its unique
        list = list.distinct()
        return ArrayList(list)
    }

    fun loadNotesHistory() {
        viewModelScope.launch {
            try {
                Bugsnag.leaveBreadcrumb("getting notes")
                val resp = salesRepository.getSalesNotes().await()
                resp.data?.let { list ->
                    notesHistory.clear()
                    notesHistory.addAll(list)
                }
            } catch (e: Exception) {
                Timber.i("getting notes err: $e")
            }
        }
    }

    fun loadDiscAndVoucherInfoHistory() {
        viewModelScope.launch {
            try {
                val result = salesRepository.fetchDiscAndVoucherInfo()
                discAndVoucherInfoHistory.clear()
                result.forEach { disc ->
                    listOf(disc.discountInfo, disc.voucherInfo).filter { !it.isNullOrBlank() }
                        .forEach { info ->
                            discAndVoucherInfoHistory.add(info.safe())
                        }
                }

            } catch (e: Exception) {
                Timber.i("load voucher or disc info failed: $e")
            }
        }
    }

    fun loadCustomerNames() {
        viewModelScope.launch {
            val data: List<String> = salesRepository.fetchCustomerNames()
            customerNames.clear()
            customerNames.addAll(data)
            Timber.d("total customer names: ${data.size}")
        }
    }

    fun loadPrinter() {
        viewModelScope.launch {
            val printers = settingRepository.getPrinterList()
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && printers.any { it.type == Constant.PRINTER_TYPE_BT }) {
                _requestPermission.postValue(Event(Manifest.permission.BLUETOOTH_CONNECT))
                _requestPermission.postValue(Event(Manifest.permission.BLUETOOTH_SCAN))
            }
        }
    }

    fun isHasVariant(id: Long): Boolean {
        return products.filter { it.productId == id }.any { it.variantFkid != null }
    }

    fun loadMultiplePrice(outletId: Int) = productRepository.loadMultiplePrice(outletId)

    fun getLinkMenuLive(outletId: Int) = productRepository.getLinkMenu(outletId)

    fun getProductsByIds(ids: List<Int>) = productRepository.getProductsByIds(ids)

    fun getTransactionCartCount() = salesRepository.getTrasactionCartCountLive()

    fun getPendingPrintCount() = salesRepository.getPendingPrintCountLive()

    fun getProductListById(id: Long) = productRepository.getProductListById(id)

    fun getProductVariantById(id: Long) = productRepository.getProductVariantListById(id)

    fun getProductByVariantId(id: Int) = productRepository.getProductByVariantId(id)

    fun getCategories() = productRepository.loadCategories()

    fun getTax(outletId: Int) = productRepository.getTax(outletId)

    fun getGratuity() = productRepository.getGratuity()

    fun getPromotionLive(outletId: Int) = promotionRepository.getActivePromotionLive(outletId)

    fun getCategoryList() = categories

    fun getProducts(outletId: Int) = productRepository.loadProducts(outletId)

    fun getProductVariant() = productRepository.loadVariant()

    fun saveTmpSales(tmpSalesEntity: TmpSalesEntity, isDirectSync: Boolean = true) {
        viewModelScope.launch { salesRepository.saveTmpSales(tmpSalesEntity, isDirectSync) }
    }

    fun updateTmpSale(tmpSalesEntity: TmpSalesEntity, isDirectSync: Boolean = true) {
        viewModelScope.launch { salesRepository.updateTmpSale(tmpSalesEntity, isDirectSync) }
    }

    fun getPendingOrderCountLive() = salesRepository.countPendingOrder()

    fun updateMenuDetail(position: Int) {
        //update disc and voucher info history
        orders.getSafe(position)?.discount?.discountInfo?.takeIf { !it.isNullOrBlank() }
            ?.let { info ->
                if (!discAndVoucherInfoHistory.any { it.lowercase() == info.lowercase() }) {
                    discAndVoucherInfoHistory.add(info)
                }
            }
    }

    fun saveTaxAndGratuity() {
        listOf(discount.discountInfo, discount.voucherInfo).filter { !it.isNullOrBlank() }
            .forEach { info ->
                if (!info.isNullOrBlank() && !discAndVoucherInfoHistory.any {
                        it.lowercase() == info.safe().lowercase()
                    }) {
                    discAndVoucherInfoHistory.add(info.safe())
                }
            }
    }

    //check if current transaction eligible for new free item, if so, show free item dialog
    fun checkFreeItemEligibility(order: Order) {
        if (!autoShowFreeEnable) return
        if (promotionList.isEmpty()) return

        viewModelScope.launch {
            val freeItemList = createFreeItemList()
            if (freeItemList.isEmpty()) return@launch

            val promoIdsEligible = ArrayList<Int>()

            //set promoId in header
            //as header should not have promo id
            freeItemList.forEachIndexed { index, promotionFree ->
                if (promotionFree.isHeader) {
                    promotionFree.promotionId = freeItemList[index + 1].promotionId
                }
            }

            //check if free item is still available to add
            freeItemList.filter { it.isHeader }.forEach { freeItemHeader ->
                Timber.d(">> Free of ${freeItemHeader.promoName} | qtyMax : ${freeItemHeader.qtyMax} | id: ${freeItemHeader.promotionId}")

                var isEligible =
                    freeItemHeader.productBuyIds.any { it == order.product?.productDetailId.safe() }
                Timber.d(">> Free item must buy: ${freeItemHeader.productBuyIds} | current id: ${order.product?.productDetailId} | eligible: $isEligible")
                //you can if current new item is related with item should bought,
                //but, it wont re-show if min order is just met requirement

                val qtySelected =
                    freeItemList.filter { it.promotionId == freeItemHeader.promotionId }
                        .sumOf { it.qtySelected }
                isEligible = qtySelected < freeItemHeader.qtyMax
                Timber.d(">> Free qty item selected: $qtySelected | is eligible: $isEligible")
                if (isEligible) {
                    promoIdsEligible.add(freeItemHeader.promotionId)
                }
            }

            if (promoIdsEligible.isNotEmpty()) {
                taskShowFreeItemDialog.postValue(promoIdsEligible.first())
            }
        }
    }

    fun setAutoShowPromoFree(selected: Boolean) {
        autoShowFreeEnable = selected
        sharedPref.putData(SharedPref.AUTOSHOW_PROMO_FREE, selected)
    }

    fun getAutoShowPromoFreeSetting(): Boolean {
        sharedPref.getString(SharedPref.AUTOSHOW_PROMO_FREE)?.let {
            autoShowFreeEnable = sharedPref.getBoolean(SharedPref.AUTOSHOW_PROMO_FREE)
        }
        return autoShowFreeEnable
    }

    fun getUnusedPromotion(): List<Promotion> {
        val grandTotal = calculateGrandTotal(false)
        //promotion.discountType == Constant.PROMO_DISC_TYPE_NOTA
        //promotion.type == "nominal" || promotion.type == "voucher" || (promotion.promoDiscountType == Constant.TYPE_NOMINAL && promotion.typeId == Constant.PROMO_TYPE_DEALS)
        //promotion.promoDiscountType == "percent" && promotion.typeId == Constant.PROMO_TYPE_DEALS
        return promoApplied.filter { it.appliedByManual && it.discountType == Constant.PROMO_DISC_TYPE_NOTA }
            .filter { it.minOrder > 0 && it.minOrder > grandTotal }
    }

    private fun isDealSpecialPriceOrDiscount(promotion: Promotion): Boolean {
        Timber.d("[promo] deals type: ${promotion.type} | ${promotion.typeId}")
        return promotion.typeId == Constant.PROMO_TYPE_DEALS && (promotion.type == "special_price" || promotion.type == "discount")
    }

    fun isItemOfDealsSpecial(order: Order): Boolean {
        if (order.promotion == null || promoApplied.isEmpty()) {
            return false
        }
        val promo = promoApplied.first()
        return isDealSpecialPriceOrDiscount(promo) && order.promotion?.promotionCode == promo.code
    }

    fun enableUpdateQty(order: Order, newQty: Int): Boolean {
        if (order.promotion == null && promoApplied.isEmpty()) {
            return true
        }

        if (promoApplied.isNotEmpty() && isDealSpecialPriceOrDiscount(promoApplied.first())) {
            val promo = promoApplied.first()
            if (promo.maxQtyPromo == null) {
                return true
            }

            val qtyAdded =
                orders.filter { p -> p.promotion?.promotionCode == promo.code && p.tmpId != order.tmpId }
                    .sumOf { it.qty } + newQty
            if ((qtyAdded > promo.maxQtyPromo.safe())) {
                _toastMessage.postValue(
                    Event(
                        ToastMessage(
                            "qty promo has reached the maximum limit",
                            type = Level.WARNING
                        )
                    )
                )
            }
            return qtyAdded <= promo.maxQtyPromo.safe()
        }

        if (order.promotion?.maxQtyPromo.safe() > 0) {
            val qtyAdded =
                orders.filter { p -> p.promotion?.promotionId == order.promotion?.promotionId && p.tmpId != order.tmpId }
                    .sumOf { it.qty } + newQty
            Timber.d("[promo] qtyAdded: $qtyAdded")
            if ((qtyAdded > order.promotion?.maxQtyPromo.safe())) {
                _toastMessage.postValue(
                    Event(
                        ToastMessage(
                            "qty promo has reached the maximum limit",
                            type = Level.WARNING
                        )
                    )
                )
            }
            return qtyAdded <= order.promotion?.maxQtyPromo.safe()
        }
        return true
    }

    fun enableChangeQty(order: Order, isAdd: Boolean): Boolean {
        if (!isAdd || (order.promotion == null)) {
            return true
        }

        var result = true
        Timber.d("[promo] maxQty: ${order.promotion?.maxQtyPromo} | ${order.promotion?.name}")
        if (order.promotion?.maxQtyPromo.safe() > 0) {
            val qtyAdded =
                orders.filter { p -> p.promotion?.promotionId == order.promotion?.promotionId }
                    .sumOf { it.qty }
            if ((qtyAdded >= order.promotion?.maxQtyPromo.safe())) {
                _toastMessage.postValue(
                    Event(
                        ToastMessage(
                            message = "qty promo has reached the maximum limit",
                            type = Level.WARNING,
                            messageResId = R.string.promo_qty_exceed_limit
                        )
                    )
                )
            }
            result = qtyAdded < order.promotion?.maxQtyPromo.safe()
        }
        return result
    }

    fun hasPotentialPromo(order: Order): Boolean {
        if (order.promotion != null) return false
        if (promotionList.isEmpty()) return false
        val promoInRange = promotionList.filter { it.isInRange }
        if (promoInRange.isEmpty()) return false

        Timber.i("#promo total promo : ${promoInRange.size}")
        promoInRange.forEach { promotion ->
            val isFullfill = isPromoTermFulfilled(promotion, order.copy(tmpId = 0))
            Timber.i("#promo isFullFill: $isFullfill (${promotion.name})")
            if (isFullfill) {
                return true
            }
        }
        return false
    }

    fun calculatePromotionItem(): Int {
        return orders.filter { it.promotion != null }.sumOf { it.promotion?.promotionValue.safe() }
    }

    private fun getMemberPromoUsage(memberId: Int) {
        val promoParentType = "member_promo"
        promotionUsage.clear()
        viewModelScope.launch {
            try {
                val result =
                    promotionRepository.getPromotionUsage(memberId, promoParentType).await()
                result.takeIf { it.status.safe() }?.data?.let {
                    promotionUsage.addAll(it)
                }
                Timber.i("promo usage of $memberId: ${Gson().toJson(promotionUsage)}")
            } catch (e: Exception) {
                _toastMessage.postValue(
                    Event(ToastMessage(e.readableError(), Level.ERROR))
                )
            }

            if (promotionList.any { it.memberType != null }) {
                reCalculateTax()
                taskRefreshTax.call()
                taskRefreshBill.call()
            }
        }
    }

    fun getSalesTagLive() = salesRepository.getSalesTagLive()
    fun loadSalesTag() {
        viewModelScope.launch {
            val result = salesRepository.fetchSalesTag(outlet?.outletId.safe())
            Timber.i("total tags: ${result.size} - ${Gson().toJson(result)}")
        }
    }

    fun generateProductImage(productName: String, description: String?) {
        viewModelScope.launch {
            try {
                _loadingDialog.postValue(Event(true))
                val imageUrl = productRepository.generateImage(productName, description)
                _generatedImageUrl.postValue(Event(imageUrl))
            } catch (e: Exception) {
                Timber.e(e)
                _toastMessage.postValue(Event(ToastMessage(e.message ?: "Failed to generate image", type = Level.ERROR)))
            } finally {
                _loadingDialog.postValue(Event(false))
            }
        }
    }

    fun saveCategoryPosition() {
        categories.forEachIndexed { index, subCategoryEntity ->
            subCategoryEntity.position = index
            Timber.d("$index. ${subCategoryEntity.name}")
        }
        //remove added categories (all category or promo)
        val cleanCategories = categories.filter { it.productCategoryId > 0 }
        Timber.i("save categories: ${Gson().toJson(cleanCategories)}")
        sharedPref.saveJson(SharedPref.SUBCATEGORY_DATA, cleanCategories)
        viewModelScope.launch(Dispatchers.IO) {
//            productRepository.saveSubCategory(cleanCategories)
        }
    }

}
