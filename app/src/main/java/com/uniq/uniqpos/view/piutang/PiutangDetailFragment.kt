package com.uniq.uniqpos.view.piutang


import android.content.Intent
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.databinding.DataBindingUtil
import androidx.fragment.app.Fragment
import com.google.gson.Gson
import com.uniq.uniqpos.R
import com.uniq.uniqpos.data.local.entity.PiutangEntity
import com.uniq.uniqpos.data.local.entity.PiutangHistoryEntity
import com.uniq.uniqpos.data.local.entity.SalesEntity
import com.uniq.uniqpos.databinding.FragmentPiutangDetailBinding
import com.uniq.uniqpos.databinding.ListItemHistoryPaidPiutangBinding
import com.uniq.uniqpos.util.safe
import com.uniq.uniqpos.util.setVisible
import com.uniq.uniqpos.util.showMessage
import com.uniq.uniqpos.view.global.GlobalAdapter
import com.uniq.uniqpos.view.payment.PaymentActivity
import com.uniq.uniqpos.view.transactionhistory.HistoryDetailActivity
import timber.log.Timber


/**
 * A simple [Fragment] subclass.
 *
 */
class PiutangDetailFragment : Fragment() {

    private var piutangEntity: PiutangEntity? = null
    private var sales: SalesEntity? = null
    private lateinit var binding: FragmentPiutangDetailBinding
    private lateinit var viewModel: PiutangViewModel
    private val piutangHistoryList = ArrayList<PiutangHistoryEntity>()
    private var isTabLayout = false

    override fun onCreateView(inflater: LayoutInflater, container: ViewGroup?,
                              savedInstanceState: Bundle?): View? {
        binding = DataBindingUtil.inflate(inflater, R.layout.fragment_piutang_detail, container, false)

        viewModel = when (activity) {
            is PiutangActivity -> (activity as PiutangActivity).viewModel
            else -> (activity as PiutangDetailActivity).viewModel
        }

        if (resources.getBoolean(R.bool.landscape_only)) {
            isTabLayout = true
        }

        arguments?.apply {
            sales = getParcelable("sales")
            piutangEntity = getParcelable("data")
            piutangEntity?.sales = sales
            binding.model = piutangEntity
            viewModel.loadPiutang(piutangEntity?.salesFkid.safe())
        }

        sales?.payments?.takeIf { it.isNotEmpty() }?.let { paymentList ->
            updatePaymentList(sales)
            Timber.i("paymentList from sales ${sales?.noNota}: ${Gson().toJson(paymentList)}")
        }

        observeData()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        binding.recviewHistoryPayment.adapter = object : GlobalAdapter<ListItemHistoryPaidPiutangBinding>(R.layout.list_item_history_paid_piutang, piutangHistoryList) {}

        binding.btnAddPayment.setOnClickListener {
            piutangEntity?.apply {
                val intent = Intent(requireContext(), PaymentActivity::class.java)
                intent.putExtra("ispiutang", true)
//                intent.putExtra("piutang", piutangEntity?.copy(unpaid = calculateUnPaid()))
                intent.putExtra("piutang", piutangEntity)
                intent.putExtra(PaymentActivity.INTENT_PAY_FLOW, "Piutang")
                startActivity(intent)
            }
        }

        binding.btnSendInvoice.setOnClickListener { context?.showMessage("Fitur ini sedang dalam tahap pengembangan!") }
        binding.txtShowSales.setOnClickListener {
            sales?.apply {
                val intent = Intent(context, HistoryDetailActivity::class.java)
                intent.putExtra("data", sales)
                startActivity(intent)
            }
        }

        binding.txtShowSales.setVisible((isTabLayout && activity is PiutangActivity) || !isTabLayout)

        observeData()
    }

    private fun calculateUnPaid(): Int {
//        val result = piutangEntity?.total.safe() - piutangHistoryList.sumBy { it.pay }
        val result =  piutangEntity?.unpaid.safe()
//        val backColor = if (result > 0) ContextCompat.getColor(context!!, R.color.blue_background) else ContextCompat.getColor(context!!, R.color.blue_background_disable)
//        val textColor = if (result > 0) Color.parseColor("#FFFFFF") else ContextCompat.getColor(context!!, R.color.blue_background_text_disable)
//        binding.btnAddPayment.setBackgroundColor(backColor)
//        binding.btnAddPayment.setTextColor(textColor)
//        binding.btnAddPayment.isEnabled = result > 0
        binding.btnAddPayment.setVisible(result > 0)
        return result
    }

    private fun observeData() {
        viewModel.getSalesByIdLive(piutangEntity?.salesFkid.safe()).observe(viewLifecycleOwner, { sales ->
            updatePaymentList(sales)
        })

        viewModel.getPiutangBySalesIdLive(piutangEntity?.salesFkid.safe()).observe(viewLifecycleOwner) { piutang ->
            piutangEntity = piutang
        }
        
//        viewModel.getPiutangHistoryByIdLive(piutangEntity?.piutangId.safe())
//                .observe(viewLifecycleOwner, {
////                   updatePaymentList(it)
//                })
    }

    //update payment list, either data should be provided
    private fun updatePaymentList(sales: SalesEntity? = null, piutangHistory: List<PiutangHistoryEntity>? = null){
        piutangHistoryList.clear()
        sales?.payments?.takeIf { it.isNotEmpty() }?.let { paymentList ->
            Timber.i("payment from ${sales.noNota} size ${paymentList.size} | methods: ${paymentList.map { it.method }}")
            val paymentPiutangList = paymentList.filter { it.method != "PIUTANG" }.map { pay ->
                PiutangHistoryEntity(total = pay.total, method = pay.method, pay = pay.pay, employeeFkid = sales.employeeID.safe(), piutangFkid = 0)
            }
            piutangHistoryList.addAll(paymentPiutangList)

        }
        piutangHistory?.let { historyList ->
//            historyList.forEach { history -> piutangHistoryList.removeIf { it.dataModified == histor } }
//            piutangHistoryList.removeAll(piutangHistoryList.filter { it.piutangFkid.toInt() != 0 })
            piutangHistoryList.addAll(historyList)
        }
        binding.recviewHistoryPayment.adapter?.notifyDataSetChanged()
        calculateUnPaid()
    }

}
