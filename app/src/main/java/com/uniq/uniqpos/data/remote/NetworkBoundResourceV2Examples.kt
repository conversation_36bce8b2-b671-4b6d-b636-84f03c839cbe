package com.uniq.uniqpos.data.remote

import androidx.lifecycle.LiveData
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.CoroutineScope
import java.time.Duration

/**
 * Examples showing how to use NetworkBoundResourceV2 and migrate from the original version.
 * 
 * This file demonstrates various usage patterns and migration strategies.
 */

/**
 * Example 1: Basic migration from original NetworkBoundResource
 * 
 * BEFORE (Original):
 * ```kotlin
 * fun getProducts(): LiveData<Resource<List<ProductEntity>>> {
 *     return object : NetworkBoundResource<List<ProductEntity>, ServerResponseList<ProductEntity>>() {
 *         override fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
 *             item?.data?.let { productDao.saveProducts(it) }
 *         }
 *         override fun loadFromDb() = productDao.getProductsLive()
 *         override fun createCall(lastSync: Long) = productService.getProducts(lastSync)
 *         override fun getLastSync() = lastSyncDao.getLastSync("ProductEntity")?.lastSync
 *     }.asLiveData
 * }
 * ```
 */
class ProductRepositoryExample(
    private val scope: CoroutineScope,
    private val productDao: Any, // ProductDao
    private val productService: Any, // ProductService
    private val lastSyncDao: Any // LastSyncDao
) {

    /**
     * AFTER (NetworkBoundResourceV2) - Traditional approach
     */
    fun getProductsV2Traditional(): LiveData<NetworkResult<List<Any>>> {
        val resource = object : NetworkBoundResourceV2<List<Any>, Any>(
            scope = scope,
            syncStrategy = SyncStrategy.CACHE_FIRST,
            entityName = "ProductEntity"
        ) {
            override suspend fun saveCallResult(item: Any?) {
                // item?.data?.let { productDao.saveProducts(it) }
            }
            
            override fun loadFromDb(): LiveData<List<Any>> {
                // return productDao.getProductsLive()
                TODO("Implement loadFromDb")
            }
            
            override fun createCall(lastSync: Long): retrofit2.Call<Any>? {
                // return productService.getProducts(lastSync)
                TODO("Implement createCall")
            }
            
            override fun getLastSync(): Long? {
                // return lastSyncDao.getLastSync("ProductEntity")?.lastSync
                return null
            }
        }
        
        resource.initialize()
        return resource.asLiveData
    }

    /**
     * AFTER (NetworkBoundResourceV2) - DSL approach
     */
    fun getProductsV2DSL(): LiveData<NetworkResult<List<Any>>> {
        val resource = networkBoundResource<List<Any>, Any>(scope, "ProductEntity") {
            syncStrategy(SyncStrategy.CACHE_FIRST)
            cachePolicy(CachePolicy(maxAge = Duration.ofHours(2)))
            retryPolicy(RetryPolicy(maxRetries = 3))
        }.build(
            saveCallResult = { item ->
                // item?.data?.let { productDao.saveProducts(it) }
            },
            loadFromDb = {
                // productDao.getProductsLive()
                TODO("Implement loadFromDb")
            },
            createCall = { lastSync ->
                // productService.getProducts(lastSync)
                null
            },
            getLastSync = {
                // lastSyncDao.getLastSync("ProductEntity")?.lastSync
                null
            }
        )
        
        resource.initialize()
        return resource.asLiveData
    }

    /**
     * Example with custom cache policy for frequently changing data
     */
    fun getOrdersWithShortCache(): LiveData<NetworkResult<List<Any>>> {
        val resource = networkBoundResource<List<Any>, Any>(scope, "OrderEntity") {
            syncStrategy(SyncStrategy.CACHE_FIRST)
            cachePolicy(CachePolicy(
                maxAge = Duration.ofMinutes(5), // Short cache for orders
                staleWhileRevalidate = true
            ))
            retryPolicy(RetryPolicy(
                maxRetries = 5,
                initialDelay = Duration.ofMillis(500)
            ))
        }.build(
            saveCallResult = { /* implementation */ },
            loadFromDb = { TODO("Implement") },
            createCall = { null },
            getLastSync = { null }
        )
        
        resource.initialize()
        return resource.asLiveData
    }

    /**
     * Example with network-first strategy for critical data
     */
    fun getCriticalDataNetworkFirst(): LiveData<NetworkResult<List<Any>>> {
        val resource = networkBoundResource<List<Any>, Any>(scope, "CriticalEntity") {
            syncStrategy(SyncStrategy.NETWORK_FIRST)
            retryPolicy(RetryPolicy(maxRetries = 5))
        }.build(
            saveCallResult = { /* implementation */ },
            loadFromDb = { TODO("Implement") },
            createCall = { null },
            getLastSync = { null }
        )
        
        resource.initialize()
        return resource.asLiveData
    }

    /**
     * Example with cache-only strategy for offline mode
     */
    fun getOfflineData(): LiveData<NetworkResult<List<Any>>> {
        val resource = networkBoundResource<List<Any>, Any>(scope, "OfflineEntity") {
            syncStrategy(SyncStrategy.CACHE_ONLY)
        }.build(
            saveCallResult = { /* not used in cache-only */ },
            loadFromDb = { TODO("Implement") },
            createCall = { null }, // not used in cache-only
            getLastSync = { null } // not used in cache-only
        )
        
        resource.initialize()
        return resource.asLiveData
    }
}

/**
 * Example showing how to handle the enhanced error types
 */
class ErrorHandlingExample {
    
    fun observeDataWithEnhancedErrorHandling(
        data: LiveData<NetworkResult<List<Any>>>
    ) {
        data.observeForever { result ->
            when (result) {
                is NetworkResult.Success -> {
                    // Handle success
                    val items = result.data
                    val isFromCache = result.fromCache
                    val lastSync = result.lastSyncTime
                    
                    if (isFromCache) {
                        // Show "showing cached data" indicator
                    }
                }
                
                is NetworkResult.Error -> {
                    // Handle different error types
                    when (val exception = result.exception) {
                        is SyncException.NetworkException -> {
                            // Network error - show retry option
                            if (result.canRetry) {
                                // Show retry button
                            }
                        }
                        is SyncException.DatabaseException -> {
                            // Database error - might need app restart
                        }
                        is SyncException.AuthenticationException -> {
                            // Auth error - redirect to login
                        }
                        is SyncException.ValidationException -> {
                            // Data validation error - log for debugging
                        }
                    }
                    
                    // Show cached data if available
                    result.cachedData?.let { cachedData ->
                        // Display cached data with error indicator
                    }
                }
                
                is NetworkResult.Loading -> {
                    // Handle loading state
                    result.cachedData?.let { cachedData ->
                        // Show cached data with loading indicator
                    }
                    
                    result.progress?.let { progress ->
                        // Show progress bar if available
                    }
                }
            }
        }
    }
}

/**
 * Example showing backward compatibility with existing Resource-based code
 */
class BackwardCompatibilityExample {
    
    fun migrateGradually(
        newData: LiveData<NetworkResult<List<Any>>>,
        oldResourceObserver: (Resource<List<Any>>) -> Unit
    ) {
        // Convert new NetworkResult to old Resource for existing observers
        newData.observeForever { networkResult ->
            val resource = networkResult.toResource()
            oldResourceObserver(resource)
        }
    }
    
    fun convertOldResourceToNew(
        oldData: LiveData<Resource<List<Any>>>
    ): LiveData<NetworkResult<List<Any>>> {
        // This would require a transformation function
        // Implementation depends on your specific needs
        TODO("Implement transformation from Resource to NetworkResult")
    }
}

/**
 * Example showing advanced configuration options
 */
class AdvancedConfigurationExample(private val scope: CoroutineScope) {
    
    fun getDataWithAdvancedConfig(): LiveData<NetworkResult<List<Any>>> {
        val resource = object : NetworkBoundResourceV2<List<Any>, Any>(
            scope = scope,
            syncStrategy = SyncStrategy.CACHE_FIRST,
            cachePolicy = CachePolicy(
                maxAge = Duration.ofHours(1),
                staleWhileRevalidate = true,
                forceRefreshThreshold = Duration.ofDays(1)
            ),
            retryPolicy = RetryPolicy(
                maxRetries = 3,
                initialDelay = Duration.ofSeconds(1),
                maxDelay = Duration.ofSeconds(30),
                backoffMultiplier = 2.0
            ),
            entityName = "AdvancedEntity"
        ) {
            override suspend fun saveCallResult(item: Any?) {
                // Custom save logic with validation
                try {
                    // Validate data before saving
                    validateData(item)
                    // Save to database
                    // Update last sync timestamp
                } catch (e: Exception) {
                    throw SyncException.ValidationException("Data validation failed", e)
                }
            }
            
            override fun loadFromDb(): LiveData<List<Any>> {
                TODO("Implement")
            }
            
            override fun createCall(lastSync: Long): retrofit2.Call<Any>? {
                TODO("Implement")
            }
            
            override fun getLastSync(): Long? {
                return null
            }
            
            override fun shouldFetchFromNetwork(cachedData: List<Any>?): Boolean {
                // Custom logic for determining when to fetch
                return cachedData == null || cachedData.isEmpty() || super.shouldFetchFromNetwork(cachedData)
            }
            
            override fun onSyncSuccess(metrics: SyncMetrics) {
                super.onSyncSuccess(metrics)
                // Custom success handling - analytics, notifications, etc.
            }
            
            override fun onFetchFailed(exception: Exception) {
                super.onFetchFailed(exception)
                // Custom error handling - analytics, user notifications, etc.
            }
            
            private fun validateData(item: Any?) {
                // Custom validation logic
            }
        }
        
        resource.initialize()
        return resource.asLiveData
    }
}
