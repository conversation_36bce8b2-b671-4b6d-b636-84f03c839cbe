# NetworkBoundResource Migration Guide

This guide explains how to migrate from the original `NetworkBoundResource` to the enhanced `NetworkBoundResourceV2`.

## Key Improvements in V2

### 🚨 Critical Fixes
- ✅ **No more GlobalScope**: Uses proper CoroutineScope to prevent memory leaks
- ✅ **Resource cleanup**: Automatic job cancellation and proper lifecycle management
- ✅ **Thread safety**: Consistent use of coroutines instead of mixed threading

### 🔧 Enhanced Features
- ✅ **Configurable sync strategies**: Cache-first, network-first, cache-only, network-only
- ✅ **Smart caching**: Configurable cache policies with age-based invalidation
- ✅ **Retry mechanisms**: Exponential backoff with configurable retry policies
- ✅ **Better error handling**: Typed exceptions and detailed error states
- ✅ **Enhanced observability**: Sync metrics and detailed logging
- ✅ **Flow support**: Modern reactive programming alongside LiveData

## Migration Steps

### Step 1: Update Dependencies
Ensure you have the latest coroutines and lifecycle dependencies:

```kotlin
implementation "org.jetbrains.kotlinx:kotlinx-coroutines-android:1.6.4"
implementation "androidx.lifecycle:lifecycle-livedata-ktx:2.6.2"
```

### Step 2: Basic Migration

**Before (Original):**
```kotlin
fun getProducts(): LiveData<Resource<List<ProductEntity>>> {
    return object : NetworkBoundResource<List<ProductEntity>, ServerResponseList<ProductEntity>>() {
        override fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
            item?.data?.let { 
                it.forEach { product -> product.synced = true }
                productDao.saveProducts(it) 
            }
            lastSyncDao.saveLastSync(LastSyncEntity("ProductEntity", item?.millis ?: 0))
        }
        override fun loadFromDb() = productDao.getProductsLive()
        override fun createCall(lastSync: Long) = productService.getProducts(outletId, lastSync)
        override fun getLastSync() = lastSyncDao.getLastSync("ProductEntity")?.lastSync
    }.asLiveData
}
```

**After (V2 - Traditional approach):**
```kotlin
fun getProducts(): LiveData<NetworkResult<List<ProductEntity>>> {
    val resource = object : NetworkBoundResourceV2<List<ProductEntity>, ServerResponseList<ProductEntity>>(
        scope = viewModelScope, // or appropriate scope
        syncStrategy = SyncStrategy.CACHE_FIRST,
        entityName = "ProductEntity"
    ) {
        override suspend fun saveCallResult(item: ServerResponseList<ProductEntity>?) {
            item?.data?.let { 
                it.forEach { product -> product.synced = true }
                productDao.saveProducts(it) 
            }
            lastSyncDao.saveLastSync(LastSyncEntity("ProductEntity", item?.millis ?: 0))
        }
        override fun loadFromDb() = productDao.getProductsLive()
        override fun createCall(lastSync: Long) = productService.getProducts(outletId, lastSync)
        override fun getLastSync() = lastSyncDao.getLastSync("ProductEntity")?.lastSync
    }
    
    resource.initialize()
    return resource.asLiveData
}
```

**After (V2 - DSL approach):**
```kotlin
fun getProducts(): LiveData<NetworkResult<List<ProductEntity>>> {
    val resource = networkBoundResource<List<ProductEntity>, ServerResponseList<ProductEntity>>(
        scope = viewModelScope,
        entityName = "ProductEntity"
    ) {
        syncStrategy(SyncStrategy.CACHE_FIRST)
        cachePolicy(CachePolicy(maxAge = Duration.ofHours(1)))
        retryPolicy(RetryPolicy(maxRetries = 3))
    }.build(
        saveCallResult = { item ->
            item?.data?.let { 
                it.forEach { product -> product.synced = true }
                productDao.saveProducts(it) 
            }
            lastSyncDao.saveLastSync(LastSyncEntity("ProductEntity", item?.millis ?: 0))
        },
        loadFromDb = { productDao.getProductsLive() },
        createCall = { lastSync -> productService.getProducts(outletId, lastSync) },
        getLastSync = { lastSyncDao.getLastSync("ProductEntity")?.lastSync }
    )
    
    resource.initialize()
    return resource.asLiveData
}
```

### Step 3: Update Observers

**Before:**
```kotlin
productRepository.getProducts().observe(this) { resource ->
    when (resource.status) {
        Status.SUCCESS -> {
            // Handle success
            val data = resource.data
        }
        Status.ERROR -> {
            // Handle error
            val error = resource.message
            val cachedData = resource.data
        }
        Status.LOADING -> {
            // Handle loading
            val cachedData = resource.data
        }
    }
}
```

**After:**
```kotlin
productRepository.getProducts().observe(this) { result ->
    when (result) {
        is NetworkResult.Success -> {
            // Handle success with more info
            val data = result.data
            val isFromCache = result.fromCache
            val lastSyncTime = result.lastSyncTime
        }
        is NetworkResult.Error -> {
            // Handle typed errors
            when (val exception = result.exception) {
                is SyncException.NetworkException -> {
                    // Network-specific error handling
                }
                is SyncException.DatabaseException -> {
                    // Database-specific error handling
                }
                // ... other error types
            }
            val cachedData = result.cachedData
            val canRetry = result.canRetry
        }
        is NetworkResult.Loading -> {
            // Handle loading with progress
            val cachedData = result.cachedData
            val progress = result.progress
        }
    }
}
```

### Step 4: Backward Compatibility

If you need to maintain compatibility with existing code:

```kotlin
// Convert NetworkResult to Resource for existing observers
fun getProductsCompatible(): LiveData<Resource<List<ProductEntity>>> {
    return getProducts().map { networkResult ->
        networkResult.toResource()
    }
}
```

## Configuration Options

### Sync Strategies

```kotlin
// Cache-first: Show cache immediately, fetch in background
syncStrategy(SyncStrategy.CACHE_FIRST)

// Network-first: Try network first, fallback to cache
syncStrategy(SyncStrategy.NETWORK_FIRST)

// Cache-only: Never fetch from network
syncStrategy(SyncStrategy.CACHE_ONLY)

// Network-only: Always fetch, no cache
syncStrategy(SyncStrategy.NETWORK_ONLY)
```

### Cache Policies

```kotlin
cachePolicy(CachePolicy(
    maxAge = Duration.ofHours(1),           // Cache validity period
    staleWhileRevalidate = true,            // Show stale data while fetching
    forceRefreshThreshold = Duration.ofDays(1) // Force refresh after this period
))
```

### Retry Policies

```kotlin
retryPolicy(RetryPolicy(
    maxRetries = 3,                         // Maximum retry attempts
    initialDelay = Duration.ofSeconds(1),   // Initial retry delay
    maxDelay = Duration.ofSeconds(30),      // Maximum retry delay
    backoffMultiplier = 2.0                 // Exponential backoff multiplier
))
```

## Best Practices

### 1. Use Appropriate Scopes
```kotlin
// In Repository (use application scope or custom scope)
class ProductRepository @Inject constructor(
    @ApplicationScope private val scope: CoroutineScope
) {
    // Use scope for long-lived operations
}

// In ViewModel (use viewModelScope)
class ProductViewModel : ViewModel() {
    fun loadProducts() = networkBoundResource(viewModelScope, "Products") { ... }
}
```

### 2. Configure Based on Data Characteristics
```kotlin
// Frequently changing data (orders, real-time data)
cachePolicy(CachePolicy(maxAge = Duration.ofMinutes(5)))

// Relatively stable data (products, categories)
cachePolicy(CachePolicy(maxAge = Duration.ofHours(4)))

// Very stable data (configuration, settings)
cachePolicy(CachePolicy(maxAge = Duration.ofDays(1)))
```

### 3. Handle Errors Appropriately
```kotlin
override fun onFetchFailed(exception: Exception) {
    when (exception) {
        is SyncException.AuthenticationException -> {
            // Redirect to login
            authManager.logout()
        }
        is SyncException.NetworkException -> {
            // Log network issues for monitoring
            analyticsService.logNetworkError(entityName, exception)
        }
    }
}
```

### 4. Monitor Performance
```kotlin
override fun onSyncSuccess(metrics: SyncMetrics) {
    // Log performance metrics
    analyticsService.logSyncMetrics(metrics)
    
    // Alert on slow syncs
    if (metrics.syncDuration > Duration.ofSeconds(10)) {
        Timber.w("Slow sync detected for ${metrics.entityType}: ${metrics.syncDuration}")
    }
}
```

## Common Migration Issues

### Issue 1: Scope Management
**Problem**: Using GlobalScope or wrong scope
**Solution**: Inject appropriate CoroutineScope

### Issue 2: Resource Leaks
**Problem**: Not calling `initialize()` or `cancel()`
**Solution**: Always call `initialize()` and `cancel()` in appropriate lifecycle methods

### Issue 3: Error Handling
**Problem**: Not handling new error types
**Solution**: Update error handling to use typed exceptions

### Issue 4: Threading
**Problem**: Blocking operations on main thread
**Solution**: Use suspend functions and proper dispatchers

## Testing

### Unit Testing
```kotlin
@Test
fun `test network bound resource with mock scope`() = runTest {
    val testScope = TestScope()
    val resource = networkBoundResource(testScope, "Test") {
        // configuration
    }.build(
        // mock implementations
    )
    
    resource.initialize()
    // assertions
}
```

### Integration Testing
```kotlin
@Test
fun `test full sync flow`() = runTest {
    // Test complete sync flow with real database and mock network
}
```
